{"Version": 1, "Hash": "wybAENuMdlxwcfxbvyUK59qa/dUN+RCI+NiUkQAO3kU=", "Source": "MedicalImageAnalysis.Web", "BasePath": "_content/MedicalImageAnalysis.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "MedicalImageAnalysis.Web\\wwwroot", "Source": "MedicalImageAnalysis.Web", "ContentRoot": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\", "BasePath": "_content/MedicalImageAnalysis.Web", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.css", "SourceId": "Blazorise.Bootstrap5", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise.Bootstrap5", "RelativePath": "blazorise.bootstrap5.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "i7bnanqjdz", "Integrity": "fv6EZTSDe/dbsGHcGi/OsXGCJ29DS9cgTkAChWURfVs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.css", "FileLength": 87508, "LastWriteTime": "2024-01-29T08:40:56+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.min.css", "SourceId": "Blazorise.Bootstrap5", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise.Bootstrap5", "RelativePath": "blazorise.bootstrap5.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bma77fm84a", "Integrity": "fWtOHgoT9ACm656TfnJzwWesenDJ0Gi+rRo/LwZkdao=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.min.css", "FileLength": 70296, "LastWriteTime": "2024-01-29T08:40:56+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\modal.js", "SourceId": "Blazorise.Bootstrap5", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise.Bootstrap5", "RelativePath": "modal.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qmfpgx199h", "Integrity": "nmBocwq71lzZvK8PgW4CmY1Lg0l13vYllZpAszJ5dXI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\modal.js", "FileLength": 3939, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\tooltip.js", "SourceId": "Blazorise.Bootstrap5", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise.Bootstrap5", "RelativePath": "tooltip.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bhsggsqt5r", "Integrity": "0SsaWZFsYhKUyxkY7AORXYg3LsjD5deznxguGQFz1eI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\tooltip.js", "FileLength": 421, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.css", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "blazorise.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "w2rkbj284c", "Integrity": "GP1G2nhvZ243mLfCXJL8hYi57H8P/HA2fmZrusF70nw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.css", "FileLength": 67982, "LastWriteTime": "2024-01-24T12:35:00+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.min.css", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "blazorise.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "czkd00wxo8", "Integrity": "eOt59ZYXwX8B9y8l2Se8MEctrFLQUV1RW2bRpVUGEsM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.min.css", "FileLength": 60000, "LastWriteTime": "2024-01-24T12:35:00+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\breakpoint.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "breakpoint.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "id0t3r0t65", "Integrity": "JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\breakpoint.js", "FileLength": 2379, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\button.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "button.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "82e040z584", "Integrity": "MDvFXabuWT407wRcLuQXQdhjsxrQrmKwCmHqBF/2Pv0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\button.js", "FileLength": 949, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\closable.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "closable.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fhfndkf66a", "Integrity": "Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\closable.js", "FileLength": 5188, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\colorPicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "colorPicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ud6cjn8zgl", "Integrity": "rmUsWEAm0eyXhjFgSQhdy2nHjMvih4MEP6uIc/0M6c0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\colorPicker.js", "FileLength": 7047, "LastWriteTime": "2024-01-29T10:41:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\datePicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "datePicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "06sjv0489f", "Integrity": "WNSbnhG3EQd+zgM7AopwQQ9dXZhSepALf9nmYFqRvw0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\datePicker.js", "FileLength": 12869, "LastWriteTime": "2024-01-29T10:41:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dragDrop.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "dragDrop.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t0yrw3o63r", "Integrity": "Aw+6VFl0UEW2TCK7RkpMkJy6TsblnZduHawm04gwQ1Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dragDrop.js", "FileLength": 2440, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dropdown.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "dropdown.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6px1262uhf", "Integrity": "2hobq2IkjtN24UIlFUGCINxX+u6Fgxj/+ezcnblloJo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dropdown.js", "FileLength": 1372, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\fileEdit.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "fileEdit.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kj461f6oua", "Integrity": "gPlzBplZsScSTrPTARk9meqO3ngwcug+icK06J7yGZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\fileEdit.js", "FileLength": 5702, "LastWriteTime": "2024-01-29T10:41:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\filePicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "filePicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iqtkjq5rwl", "Integrity": "doH1FHSo3w/yVZRMV5GNOPiSbN9YpX73mIHvumKyzCU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\filePicker.js", "FileLength": 2247, "LastWriteTime": "2024-01-29T10:41:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\floatingUi.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "floatingUi.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qlnisu95dt", "Integrity": "AhQWnKESfGi41a+YG6ZXMcP6I9LvRj/XbBvK3v35tgg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\floatingUi.js", "FileLength": 1681, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\inputMask.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "inputMask.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ldvsmunupm", "Integrity": "2Ys+oINrQx9tepEH1xCtYv+rwiKkA3eYR7RZo7hJ+Os=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\inputMask.js", "FileLength": 2604, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\io.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "io.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jpt4u823i0", "Integrity": "mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\io.js", "FileLength": 4775, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\memoEdit.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "memoEdit.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pdvbj4pezq", "Integrity": "Y/lK1dVrt/awy16dDPYfp7y+ovL/RxKAC6rQPqsC31Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\memoEdit.js", "FileLength": 3595, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\numericPicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "numericPicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1ygi1dfwlx", "Integrity": "t/Rbw/26RsfEUW/2BkmSxoTWa53XUgU6bfXrzEsKgSY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\numericPicker.js", "FileLength": 7352, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\observer.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "observer.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s1nsmqqjya", "Integrity": "UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\observer.js", "FileLength": 3682, "LastWriteTime": "2023-12-05T11:19:44+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\table.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "table.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "oky876bcag", "Integrity": "CFBEPQJo0UgveddHA+XQ6Yxey/g3l2xDCRUhN7s2yhE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\table.js", "FileLength": 7739, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\textEdit.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "textEdit.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ph79pqpyod", "Integrity": "0wC7htxIWOHGD19Yc1tPqX0kn7k1ILicd64Bz7cJu2A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\textEdit.js", "FileLength": 1722, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\theme.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "theme.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7mew5qey9y", "Integrity": "/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\theme.js", "FileLength": 1487, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\timePicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "timePicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "g5xqvxvlnv", "Integrity": "VP39HLpfjrx9sw8Yf/A3FwS1g29R5R8NALQbsYv8CDg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\timePicker.js", "FileLength": 6161, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\tooltip.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "tooltip.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hqt248o0ba", "Integrity": "CSdK5FabrdtmiH8JySFriuSwszOxX0U6IpoJVlCLpW8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\tooltip.js", "FileLength": 54020, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\utilities.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "utilities.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnoi3d7w4a", "Integrity": "VfSBGhC9/G9Qh7ARVB4Mm9X+hoRfC4vg/yYVXMtVjU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\utilities.js", "FileLength": 9501, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\DateTimeMaskValidator.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "validators/DateTimeMaskValidator.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9sjnuohl93", "Integrity": "6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\DateTimeMaskValidator.js", "FileLength": 651, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NoValidator.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "validators/NoValidator.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qgmopykj85", "Integrity": "RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NoValidator.js", "FileLength": 113, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NumericMaskValidator.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "validators/NumericMaskValidator.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rdf588x9o3", "Integrity": "4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NumericMaskValidator.js", "FileLength": 6408, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\RegExMaskValidator.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "validators/RegExMaskValidator.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4rmwhz9udx", "Integrity": "0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\RegExMaskValidator.js", "FileLength": 633, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\autoNumeric.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/autoNumeric.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cs38ynhyrk", "Integrity": "LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\autoNumeric.js", "FileLength": 219906, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Behave.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/Behave.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k77rf775v5", "Integrity": "QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Behave.js", "FileLength": 9317, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\flatpickr.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/flatpickr.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "edp1606bnc", "Integrity": "TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\flatpickr.js", "FileLength": 62541, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui-core.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/floating-ui-core.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2wlo7snjy4", "Integrity": "0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui-core.js", "FileLength": 15103, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/floating-ui.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5mbjrq4df3", "Integrity": "cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui.js", "FileLength": 10628, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\inputmask.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/inputmask.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kogj7cf2d4", "Integrity": "zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\inputmask.js", "FileLength": 140250, "LastWriteTime": "2024-01-24T12:33:34+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\jsencrypt.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/jsencrypt.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pybrzh7rdh", "Integrity": "UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\jsencrypt.js", "FileLength": 55434, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Pickr.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/Pickr.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qovnesw5rb", "Integrity": "1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Pickr.js", "FileLength": 27690, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\sha512.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/sha512.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "luevd9t85z", "Integrity": "E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\sha512.js", "FileLength": 17899, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\css\\app.css", "SourceId": "MedicalImageAnalysis.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\", "BasePath": "_content/MedicalImageAnalysis.Web", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0009rc8gzr", "Integrity": "wGxqbCH3sIXdaU/DSpaKuO4wzzxgqsEnJYUys9rrWbM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 6817, "LastWriteTime": "2025-07-22T13:38:24+00:00"}, {"Identity": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "MedicalImageAnalysis.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\", "BasePath": "_content/MedicalImageAnalysis.Web", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "g69rteb3dh", "Integrity": "OAyxROSN+2sdWB8zk2irIwXI2tJzhEPFljclphHLIzU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 5289, "LastWriteTime": "2025-07-22T23:43:26+00:00"}, {"Identity": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\js\\app.js", "SourceId": "MedicalImageAnalysis.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\", "BasePath": "_content/MedicalImageAnalysis.Web", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d3kcq3z4hl", "Integrity": "R5kvSQY4v7q9ukQbehDg5tnlg+1Vp7wGgYznlsmod4s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js", "FileLength": 9208, "LastWriteTime": "2025-07-22T13:34:48+00:00"}, {"Identity": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\js\\directory-manager.js", "SourceId": "MedicalImageAnalysis.Web", "SourceType": "Discovered", "ContentRoot": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\", "BasePath": "_content/MedicalImageAnalysis.Web", "RelativePath": "js/directory-manager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lbp7dec8vk", "Integrity": "D58iUiaAN/eSwgOItXDPn7sE4NcedCkKIRQEc/e0pUc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\directory-manager.js", "FileLength": 9390, "LastWriteTime": "2025-07-22T13:33:47+00:00"}], "Endpoints": [{"Route": "_content/Blazorise.Bootstrap5/blazorise.bootstrap5.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87508"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fv6EZTSDe/dbsGHcGi/OsXGCJ29DS9cgTkAChWURfVs=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 08:40:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fv6EZTSDe/dbsGHcGi/OsXGCJ29DS9cgTkAChWURfVs="}]}, {"Route": "_content/Blazorise.Bootstrap5/blazorise.bootstrap5.min.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70296"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fWtOHgoT9ACm656TfnJzwWesenDJ0Gi+rRo/LwZkdao=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 08:40:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fWtOHgoT9ACm656TfnJzwWesenDJ0Gi+rRo/LwZkdao="}]}, {"Route": "_content/Blazorise.Bootstrap5/modal.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\modal.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3939"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nmBocwq71lzZvK8PgW4CmY1Lg0l13vYllZpAszJ5dXI=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nmBocwq71lzZvK8PgW4CmY1Lg0l13vYllZpAszJ5dXI="}]}, {"Route": "_content/Blazorise.Bootstrap5/tooltip.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\tooltip.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "421"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0SsaWZFsYhKUyxkY7AORXYg3LsjD5deznxguGQFz1eI=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0SsaWZFsYhKUyxkY7AORXYg3LsjD5deznxguGQFz1eI="}]}, {"Route": "_content/Blazorise/blazorise.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "67982"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GP1G2nhvZ243mLfCXJL8hYi57H8P/HA2fmZrusF70nw=\""}, {"Name": "Last-Modified", "Value": "Wed, 24 Jan 2024 12:35:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GP1G2nhvZ243mLfCXJL8hYi57H8P/HA2fmZrusF70nw="}]}, {"Route": "_content/Blazorise/blazorise.min.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60000"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eOt59ZYXwX8B9y8l2Se8MEctrFLQUV1RW2bRpVUGEsM=\""}, {"Name": "Last-Modified", "Value": "Wed, 24 Jan 2024 12:35:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eOt59ZYXwX8B9y8l2Se8MEctrFLQUV1RW2bRpVUGEsM="}]}, {"Route": "_content/Blazorise/breakpoint.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\breakpoint.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2379"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4="}]}, {"Route": "_content/Blazorise/button.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\button.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MDvFXabuWT407wRcLuQXQdhjsxrQrmKwCmHqBF/2Pv0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MDvFXabuWT407wRcLuQXQdhjsxrQrmKwCmHqBF/2Pv0="}]}, {"Route": "_content/Blazorise/closable.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\closable.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5188"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No="}]}, {"Route": "_content/Blazorise/colorPicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\colorPicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7047"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rmUsWEAm0eyXhjFgSQhdy2nHjMvih4MEP6uIc/0M6c0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rmUsWEAm0eyXhjFgSQhdy2nHjMvih4MEP6uIc/0M6c0="}]}, {"Route": "_content/Blazorise/datePicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\datePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12869"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WNSbnhG3EQd+zgM7AopwQQ9dXZhSepALf9nmYFqRvw0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WNSbnhG3EQd+zgM7AopwQQ9dXZhSepALf9nmYFqRvw0="}]}, {"Route": "_content/Blazorise/dragDrop.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dragDrop.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2440"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Aw+6VFl0UEW2TCK7RkpMkJy6TsblnZduHawm04gwQ1Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Aw+6VFl0UEW2TCK7RkpMkJy6TsblnZduHawm04gwQ1Q="}]}, {"Route": "_content/Blazorise/dropdown.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dropdown.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1372"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2hobq2IkjtN24UIlFUGCINxX+u6Fgxj/+ezcnblloJo=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2hobq2IkjtN24UIlFUGCINxX+u6Fgxj/+ezcnblloJo="}]}, {"Route": "_content/Blazorise/fileEdit.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\fileEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5702"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gPlzBplZsScSTrPTARk9meqO3ngwcug+icK06J7yGZE=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gPlzBplZsScSTrPTARk9meqO3ngwcug+icK06J7yGZE="}]}, {"Route": "_content/Blazorise/filePicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\filePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"doH1FHSo3w/yVZRMV5GNOPiSbN9YpX73mIHvumKyzCU=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-doH1FHSo3w/yVZRMV5GNOPiSbN9YpX73mIHvumKyzCU="}]}, {"Route": "_content/Blazorise/floatingUi.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\floatingUi.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1681"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AhQWnKESfGi41a+YG6ZXMcP6I9LvRj/XbBvK3v35tgg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AhQWnKESfGi41a+YG6ZXMcP6I9LvRj/XbBvK3v35tgg="}]}, {"Route": "_content/Blazorise/inputMask.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\inputMask.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2604"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2Ys+oINrQx9tepEH1xCtYv+rwiKkA3eYR7RZo7hJ+Os=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2Ys+oINrQx9tepEH1xCtYv+rwiKkA3eYR7RZo7hJ+Os="}]}, {"Route": "_content/Blazorise/io.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\io.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4775"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY="}]}, {"Route": "_content/Blazorise/memoEdit.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\memoEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3595"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y/lK1dVrt/awy16dDPYfp7y+ovL/RxKAC6rQPqsC31Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y/lK1dVrt/awy16dDPYfp7y+ovL/RxKAC6rQPqsC31Y="}]}, {"Route": "_content/Blazorise/numericPicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\numericPicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7352"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"t/Rbw/26RsfEUW/2BkmSxoTWa53XUgU6bfXrzEsKgSY=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-t/Rbw/26RsfEUW/2BkmSxoTWa53XUgU6bfXrzEsKgSY="}]}, {"Route": "_content/Blazorise/observer.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\observer.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3682"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Dec 2023 11:19:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130="}]}, {"Route": "_content/Blazorise/table.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\table.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7739"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CFBEPQJo0UgveddHA+XQ6Yxey/g3l2xDCRUhN7s2yhE=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CFBEPQJo0UgveddHA+XQ6Yxey/g3l2xDCRUhN7s2yhE="}]}, {"Route": "_content/Blazorise/textEdit.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\textEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1722"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0wC7htxIWOHGD19Yc1tPqX0kn7k1ILicd64Bz7cJu2A=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0wC7htxIWOHGD19Yc1tPqX0kn7k1ILicd64Bz7cJu2A="}]}, {"Route": "_content/Blazorise/theme.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1487"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA="}]}, {"Route": "_content/Blazorise/timePicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\timePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6161"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VP39HLpfjrx9sw8Yf/A3FwS1g29R5R8NALQbsYv8CDg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VP39HLpfjrx9sw8Yf/A3FwS1g29R5R8NALQbsYv8CDg="}]}, {"Route": "_content/Blazorise/tooltip.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\tooltip.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54020"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CSdK5FabrdtmiH8JySFriuSwszOxX0U6IpoJVlCLpW8=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CSdK5FabrdtmiH8JySFriuSwszOxX0U6IpoJVlCLpW8="}]}, {"Route": "_content/Blazorise/utilities.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\utilities.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VfSBGhC9/G9Qh7ARVB4Mm9X+hoRfC4vg/yYVXMtVjU8=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VfSBGhC9/G9Qh7ARVB4Mm9X+hoRfC4vg/yYVXMtVjU8="}]}, {"Route": "_content/Blazorise/validators/DateTimeMaskValidator.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\DateTimeMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk="}]}, {"Route": "_content/Blazorise/validators/NoValidator.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NoValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw="}]}, {"Route": "_content/Blazorise/validators/NumericMaskValidator.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NumericMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6408"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE="}]}, {"Route": "_content/Blazorise/validators/RegExMaskValidator.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\RegExMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM="}]}, {"Route": "_content/Blazorise/vendors/autoNumeric.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\autoNumeric.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "219906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo="}]}, {"Route": "_content/Blazorise/vendors/Behave.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Behave.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9317"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE="}]}, {"Route": "_content/Blazorise/vendors/flatpickr.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\flatpickr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "62541"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88="}]}, {"Route": "_content/Blazorise/vendors/floating-ui-core.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui-core.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15103"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0="}]}, {"Route": "_content/Blazorise/vendors/floating-ui.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10628"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI="}]}, {"Route": "_content/Blazorise/vendors/inputmask.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\inputmask.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "140250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs=\""}, {"Name": "Last-Modified", "Value": "Wed, 24 Jan 2024 12:33:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs="}]}, {"Route": "_content/Blazorise/vendors/jsencrypt.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\jsencrypt.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55434"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM="}]}, {"Route": "_content/Blazorise/vendors/Pickr.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Pickr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27690"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM="}]}, {"Route": "_content/Blazorise/vendors/sha512.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\sha512.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17899"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU="}]}, {"Route": "css/app.0009rc8gzr.css", "AssetFile": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6817"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wGxqbCH3sIXdaU/DSpaKuO4wzzxgqsEnJYUys9rrWbM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:38:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0009rc8gzr"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-wGxqbCH3sIXdaU/DSpaKuO4wzzxgqsEnJYUys9rrWbM="}]}, {"Route": "css/app.css", "AssetFile": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6817"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wGxqbCH3sIXdaU/DSpaKuO4wzzxgqsEnJYUys9rrWbM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:38:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wGxqbCH3sIXdaU/DSpaKuO4wzzxgqsEnJYUys9rrWbM="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5289"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OAyxROSN+2sdWB8zk2irIwXI2tJzhEPFljclphHLIzU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 23:43:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OAyxROSN+2sdWB8zk2irIwXI2tJzhEPFljclphHLIzU="}]}, {"Route": "css/bootstrap/bootstrap.min.g69rteb3dh.css", "AssetFile": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5289"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OAyxROSN+2sdWB8zk2irIwXI2tJzhEPFljclphHLIzU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 23:43:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g69rteb3dh"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-OAyxROSN+2sdWB8zk2irIwXI2tJzhEPFljclphHLIzU="}]}, {"Route": "js/app.d3kcq3z4hl.js", "AssetFile": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9208"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R5kvSQY4v7q9ukQbehDg5tnlg+1Vp7wGgYznlsmod4s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:34:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d3kcq3z4hl"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-R5kvSQY4v7q9ukQbehDg5tnlg+1Vp7wGgYznlsmod4s="}]}, {"Route": "js/app.js", "AssetFile": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9208"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"R5kvSQY4v7q9ukQbehDg5tnlg+1Vp7wGgYznlsmod4s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:34:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R5kvSQY4v7q9ukQbehDg5tnlg+1Vp7wGgYznlsmod4s="}]}, {"Route": "js/directory-manager.js", "AssetFile": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\js\\directory-manager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"D58iUiaAN/eSwgOItXDPn7sE4NcedCkKIRQEc/e0pUc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:33:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D58iUiaAN/eSwgOItXDPn7sE4NcedCkKIRQEc/e0pUc="}]}, {"Route": "js/directory-manager.lbp7dec8vk.js", "AssetFile": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Web\\wwwroot\\js\\directory-manager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"D58iUiaAN/eSwgOItXDPn7sE4NcedCkKIRQEc/e0pUc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:33:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lbp7dec8vk"}, {"Name": "label", "Value": "js/directory-manager.js"}, {"Name": "integrity", "Value": "sha256-D58iUiaAN/eSwgOItXDPn7sE4NcedCkKIRQEc/e0pUc="}]}]}