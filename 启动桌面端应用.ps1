# 医学影像解析系统 - 桌面端应用启动器
# PowerShell 版本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "医学影像解析系统 - 桌面端应用启动器" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "正在启动医学影像解析系统桌面端应用..." -ForegroundColor Green
Write-Host ""

# 检查是否已有应用在运行
$runningProcesses = Get-Process -Name "MedicalImageAnalysis.Wpf" -ErrorAction SilentlyContinue
if ($runningProcesses) {
    Write-Host "检测到应用已在运行中..." -ForegroundColor Yellow
    Write-Host "进程ID: $($runningProcesses.Id -join ', ')" -ForegroundColor Yellow
    Write-Host "如果看不到窗口，请检查任务栏或最小化的窗口。" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按回车键退出"
    exit
}

# 检查应用是否存在
$appPath = "src\MedicalImageAnalysis.Wpf\bin\Debug\net8.0-windows\MedicalImageAnalysis.Wpf.exe"
if (-not (Test-Path $appPath)) {
    Write-Host "应用未构建，正在构建..." -ForegroundColor Yellow
    try {
        dotnet build "src\MedicalImageAnalysis.Wpf\MedicalImageAnalysis.Wpf.csproj" --configuration Debug
        if ($LASTEXITCODE -ne 0) {
            throw "构建失败"
        }
        Write-Host "构建成功！" -ForegroundColor Green
    }
    catch {
        Write-Host "构建失败！请检查错误信息。" -ForegroundColor Red
        Read-Host "按回车键退出"
        exit 1
    }
}

# 启动应用
Write-Host "启动应用..." -ForegroundColor Green
try {
    Start-Process -FilePath $appPath -WorkingDirectory (Get-Location)
    Write-Host "应用启动命令已执行。" -ForegroundColor Green
}
catch {
    Write-Host "启动失败：$($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host ""
Write-Host "应用启动成功！" -ForegroundColor Green
Write-Host "如果应用没有显示，请检查：" -ForegroundColor Yellow
Write-Host "1. 任务栏是否有应用图标" -ForegroundColor Yellow
Write-Host "2. 是否被最小化" -ForegroundColor Yellow
Write-Host "3. 查看日志文件：src\MedicalImageAnalysis.Wpf\bin\Debug\net8.0-windows\logs\" -ForegroundColor Yellow
Write-Host ""

# 等待几秒钟检查应用是否成功启动
Start-Sleep -Seconds 3
$newProcesses = Get-Process -Name "MedicalImageAnalysis.Wpf" -ErrorAction SilentlyContinue
if ($newProcesses) {
    Write-Host "✓ 应用进程已启动 (PID: $($newProcesses.Id -join ', '))" -ForegroundColor Green
} else {
    Write-Host "⚠ 未检测到应用进程，可能启动失败" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "按回车键退出"
