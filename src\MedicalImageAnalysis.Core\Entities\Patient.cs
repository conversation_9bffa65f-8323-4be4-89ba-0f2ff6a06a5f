using System.ComponentModel.DataAnnotations;

namespace MedicalImageAnalysis.Core.Entities;

/// <summary>
/// 患者信息实体
/// </summary>
public class Patient
{
    /// <summary>
    /// 患者唯一标识符
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 患者ID
    /// </summary>
    [Required]
    [StringLength(64)]
    public string PatientId { get; set; } = string.Empty;

    /// <summary>
    /// 患者姓名
    /// </summary>
    [StringLength(256)]
    public string PatientName { get; set; } = string.Empty;

    /// <summary>
    /// 患者出生日期
    /// </summary>
    public DateTime? PatientBirthDate { get; set; }

    /// <summary>
    /// 患者性别
    /// </summary>
    [StringLength(1)]
    public string PatientSex { get; set; } = string.Empty;

    /// <summary>
    /// 患者年龄
    /// </summary>
    [StringLength(4)]
    public string PatientAge { get; set; } = string.Empty;

    /// <summary>
    /// 患者体重
    /// </summary>
    [StringLength(16)]
    public string PatientWeight { get; set; } = string.Empty;

    /// <summary>
    /// 患者身高
    /// </summary>
    [StringLength(16)]
    public string PatientSize { get; set; } = string.Empty;

    /// <summary>
    /// 种族群体
    /// </summary>
    [StringLength(16)]
    public string EthnicGroup { get; set; } = string.Empty;

    /// <summary>
    /// 患者备注
    /// </summary>
    [StringLength(1024)]
    public string PatientComments { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // 导航属性
    public virtual ICollection<DicomStudy> Studies { get; set; } = new List<DicomStudy>();
}
