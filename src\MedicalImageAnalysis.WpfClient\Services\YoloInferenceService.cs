using Microsoft.Extensions.Logging;
using System.IO;
using System.Text.Json;
using System.Diagnostics;
using MedicalImageAnalysis.WpfClient.Models;

namespace MedicalImageAnalysis.WpfClient.Services;

/// <summary>
/// YOLO推理服务接口
/// </summary>
public interface IYoloInferenceService
{
    Task<List<YoloDetection>> DetectAsync(string imagePath, YoloInferenceConfig config);
    Task<List<YoloDetection>> DetectFromBytesAsync(byte[] imageData, YoloInferenceConfig config);
    Task<bool> IsModelAvailableAsync(string modelPath);
    Task<List<string>> GetAvailableModelsAsync();
}

/// <summary>
/// YOLO推理服务实现
/// </summary>
public class YoloInferenceService : IYoloInferenceService
{
    private readonly ILogger<YoloInferenceService> _logger;
    private readonly IApiService _apiService;
    private readonly string _pythonScriptPath;
    private readonly string _modelsDirectory;

    public YoloInferenceService(
        ILogger<YoloInferenceService> logger,
        IApiService apiService)
    {
        _logger = logger;
        _apiService = apiService;
        
        // 设置Python脚本和模型目录路径
        _pythonScriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Scripts", "yolo_inference.py");
        _modelsDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
                                       "MedicalImageAnalysis", "Models");
        
        // 确保目录存在
        Directory.CreateDirectory(_modelsDirectory);
        Directory.CreateDirectory(Path.GetDirectoryName(_pythonScriptPath)!);
        
        // 创建Python推理脚本
        CreatePythonInferenceScript();
    }

    /// <summary>
    /// 检测图像中的目标
    /// </summary>
    public async Task<List<YoloDetection>> DetectAsync(string imagePath, YoloInferenceConfig config)
    {
        try
        {
            if (!File.Exists(imagePath))
            {
                _logger.LogError("图像文件不存在: {ImagePath}", imagePath);
                return new List<YoloDetection>();
            }

            _logger.LogInformation("开始YOLO检测: {ImagePath}", imagePath);

            // 首先尝试通过API服务进行检测
            try
            {
                var imageBytes = await File.ReadAllBytesAsync(imagePath);
                var apiDetections = await _apiService.RunYoloInferenceAsync(imageBytes, config.ModelPath, config.Confidence);
                
                if (apiDetections != null && apiDetections.Count > 0)
                {
                    return ConvertApiDetections(apiDetections);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "API检测失败，尝试本地检测");
            }

            // 如果API失败，尝试本地Python脚本检测
            return await RunLocalInferenceAsync(imagePath, config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YOLO检测失败: {ImagePath}", imagePath);
            return new List<YoloDetection>();
        }
    }

    /// <summary>
    /// 从字节数据检测目标
    /// </summary>
    public async Task<List<YoloDetection>> DetectFromBytesAsync(byte[] imageData, YoloInferenceConfig config)
    {
        try
        {
            // 保存临时图像文件
            var tempImagePath = Path.GetTempFileName() + ".jpg";
            await File.WriteAllBytesAsync(tempImagePath, imageData);

            try
            {
                return await DetectAsync(tempImagePath, config);
            }
            finally
            {
                // 清理临时文件
                if (File.Exists(tempImagePath))
                {
                    File.Delete(tempImagePath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从字节数据进行YOLO检测失败");
            return new List<YoloDetection>();
        }
    }

    /// <summary>
    /// 检查模型是否可用
    /// </summary>
    public async Task<bool> IsModelAvailableAsync(string modelPath)
    {
        await Task.CompletedTask;
        return File.Exists(modelPath);
    }

    /// <summary>
    /// 获取可用的模型列表
    /// </summary>
    public async Task<List<string>> GetAvailableModelsAsync()
    {
        await Task.CompletedTask;
        
        var models = new List<string>();
        
        try
        {
            if (Directory.Exists(_modelsDirectory))
            {
                var modelFiles = Directory.GetFiles(_modelsDirectory, "*.pt", SearchOption.AllDirectories)
                                         .Concat(Directory.GetFiles(_modelsDirectory, "*.onnx", SearchOption.AllDirectories))
                                         .ToList();
                
                models.AddRange(modelFiles);
            }

            // 添加一些默认模型路径
            var defaultModels = new[]
            {
                "yolo11n.pt",
                "yolo11s.pt", 
                "yolo11m.pt",
                "yolo11l.pt",
                "yolo11x.pt"
            };

            foreach (var model in defaultModels)
            {
                var fullPath = Path.Combine(_modelsDirectory, model);
                if (!models.Contains(fullPath))
                {
                    models.Add(fullPath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用模型列表失败");
        }

        return models;
    }

    /// <summary>
    /// 运行本地推理
    /// </summary>
    private async Task<List<YoloDetection>> RunLocalInferenceAsync(string imagePath, YoloInferenceConfig config)
    {
        try
        {
            if (!File.Exists(_pythonScriptPath))
            {
                _logger.LogWarning("Python推理脚本不存在，返回模拟结果");
                return GenerateMockDetections();
            }

            // 准备推理参数
            var args = $"\"{_pythonScriptPath}\" \"{imagePath}\" \"{config.ModelPath}\" {config.Confidence} {config.IoU}";
            
            var processInfo = new ProcessStartInfo
            {
                FileName = "python",
                Arguments = args,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var process = Process.Start(processInfo);
            if (process == null)
            {
                _logger.LogError("无法启动Python进程");
                return GenerateMockDetections();
            }

            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            
            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
            {
                _logger.LogError("Python推理失败: {Error}", error);
                return GenerateMockDetections();
            }

            // 解析输出结果
            if (!string.IsNullOrEmpty(output))
            {
                try
                {
                    var detections = JsonSerializer.Deserialize<List<YoloDetection>>(output);
                    return detections ?? new List<YoloDetection>();
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "解析推理结果失败: {Output}", output);
                }
            }

            return GenerateMockDetections();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "本地推理失败");
            return GenerateMockDetections();
        }
    }

    /// <summary>
    /// 转换API检测结果
    /// </summary>
    private List<YoloDetection> ConvertApiDetections(List<object> apiDetections)
    {
        var detections = new List<YoloDetection>();
        
        // 这里需要根据实际的API返回格式进行转换
        // 目前返回模拟结果
        return GenerateMockDetections();
    }

    /// <summary>
    /// 生成模拟检测结果
    /// </summary>
    private List<YoloDetection> GenerateMockDetections()
    {
        var random = new Random();
        var detections = new List<YoloDetection>();

        // 生成1-3个模拟检测结果
        var count = random.Next(1, 4);
        var labels = new[] { "肿瘤", "病变", "异常区域", "钙化" };

        for (int i = 0; i < count; i++)
        {
            detections.Add(new YoloDetection
            {
                Label = labels[random.Next(labels.Length)],
                Confidence = 0.6 + random.NextDouble() * 0.4, // 0.6-1.0
                X = random.Next(50, 400),
                Y = random.Next(50, 400),
                Width = random.Next(50, 150),
                Height = random.Next(50, 150),
                ClassId = random.Next(labels.Length)
            });
        }

        return detections;
    }

    /// <summary>
    /// 创建Python推理脚本
    /// </summary>
    private void CreatePythonInferenceScript()
    {
        try
        {
            var scriptContent = @"
import sys
import json
import cv2
import numpy as np
from pathlib import Path

def main():
    if len(sys.argv) < 5:
        print('Usage: python yolo_inference.py <image_path> <model_path> <confidence> <iou>')
        sys.exit(1)
    
    image_path = sys.argv[1]
    model_path = sys.argv[2]
    confidence = float(sys.argv[3])
    iou = float(sys.argv[4])
    
    try:
        # 这里应该是实际的YOLO推理代码
        # 目前返回模拟结果
        detections = [
            {
                'Label': '示例检测',
                'Confidence': 0.85,
                'X': 100,
                'Y': 100,
                'Width': 80,
                'Height': 80,
                'ClassId': 0
            }
        ]
        
        print(json.dumps(detections))
        
    except Exception as e:
        print(f'Error: {str(e)}', file=sys.stderr)
        sys.exit(1)

if __name__ == '__main__':
    main()
";

            File.WriteAllText(_pythonScriptPath, scriptContent);
            _logger.LogInformation("Python推理脚本已创建: {ScriptPath}", _pythonScriptPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建Python推理脚本失败");
        }
    }
}

/// <summary>
/// YOLO推理配置
/// </summary>
public class YoloInferenceConfig
{
    public string ModelPath { get; set; } = "";
    public double Confidence { get; set; } = 0.5;
    public double IoU { get; set; } = 0.45;
    public int ImageSize { get; set; } = 640;
    public int MaxDetections { get; set; } = 300;
}

/// <summary>
/// YOLO检测结果
/// </summary>
public class YoloDetection
{
    public string Label { get; set; } = "";
    public double Confidence { get; set; }
    public int X { get; set; }
    public int Y { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public int ClassId { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}
