using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.WpfClient.Services;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
// using System.Management; // 暂时移除，避免依赖问题

namespace MedicalImageAnalysis.WpfClient.ViewModels;

/// <summary>
/// 系统监控ViewModel
/// </summary>
public partial class SystemMonitorViewModel : ObservableObject
{
    private readonly ILogger<SystemMonitorViewModel> _logger;
    private readonly INotificationService _notificationService;
    private readonly PerformanceCounter _cpuCounter;
    private readonly PerformanceCounter _memoryCounter;
    private readonly Timer _updateTimer;

    [ObservableProperty]
    private double _cpuUsage = 0;

    [ObservableProperty]
    private double _memoryUsage = 0;

    [ObservableProperty]
    private long _totalMemory = 0;

    [ObservableProperty]
    private long _availableMemory = 0;

    [ObservableProperty]
    private long _usedMemory = 0;

    [ObservableProperty]
    private double _diskUsage = 0;

    [ObservableProperty]
    private long _totalDiskSpace = 0;

    [ObservableProperty]
    private long _freeDiskSpace = 0;

    [ObservableProperty]
    private string _systemInfo = "";

    [ObservableProperty]
    private string _gpuInfo = "";

    [ObservableProperty]
    private double _gpuUsage = 0;

    [ObservableProperty]
    private long _gpuMemoryUsed = 0;

    [ObservableProperty]
    private long _gpuMemoryTotal = 0;

    [ObservableProperty]
    private ObservableCollection<ProcessInfo> _processes = new();

    [ObservableProperty]
    private ProcessInfo? _selectedProcess;

    [ObservableProperty]
    private ObservableCollection<ServiceInfo> _services = new();

    [ObservableProperty]
    private ServiceInfo? _selectedService;

    [ObservableProperty]
    private ObservableCollection<LogEntry> _systemLogs = new();

    [ObservableProperty]
    private bool _isMonitoring = true;

    [ObservableProperty]
    private int _updateInterval = 2000; // 毫秒

    [ObservableProperty]
    private string _networkStatus = "正常";

    [ObservableProperty]
    private double _networkUpload = 0;

    [ObservableProperty]
    private double _networkDownload = 0;

    [ObservableProperty]
    private string _statusMessage = "系统监控运行中";

    [ObservableProperty]
    private DateTime _lastUpdateTime = DateTime.Now;

    public SystemMonitorViewModel(
        ILogger<SystemMonitorViewModel> logger,
        INotificationService notificationService)
    {
        _logger = logger;
        _notificationService = notificationService;

        // 初始化性能计数器
        _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
        _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");

        // 初始化定时器
        _updateTimer = new Timer(UpdateSystemInfo, null, 0, UpdateInterval);

        InitializeSystemInfo();
        LoadProcesses();
        LoadServices();
    }

    /// <summary>
    /// 开始监控
    /// </summary>
    [RelayCommand]
    private void StartMonitoring()
    {
        if (!IsMonitoring)
        {
            IsMonitoring = true;
            _updateTimer.Change(0, UpdateInterval);
            StatusMessage = "系统监控已开始";
            AddLogEntry("INFO", "系统监控已开始");
        }
    }

    /// <summary>
    /// 停止监控
    /// </summary>
    [RelayCommand]
    private void StopMonitoring()
    {
        if (IsMonitoring)
        {
            IsMonitoring = false;
            _updateTimer.Change(Timeout.Infinite, Timeout.Infinite);
            StatusMessage = "系统监控已停止";
            AddLogEntry("INFO", "系统监控已停止");
        }
    }

    /// <summary>
    /// 刷新进程列表
    /// </summary>
    [RelayCommand]
    private void RefreshProcesses()
    {
        LoadProcesses();
        StatusMessage = "进程列表已刷新";
    }

    /// <summary>
    /// 刷新服务列表
    /// </summary>
    [RelayCommand]
    private void RefreshServices()
    {
        LoadServices();
        StatusMessage = "服务列表已刷新";
    }

    /// <summary>
    /// 结束选中进程
    /// </summary>
    [RelayCommand]
    private async Task KillSelectedProcess()
    {
        try
        {
            if (SelectedProcess == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择要结束的进程");
                return;
            }

            var process = Process.GetProcessById(SelectedProcess.Id);
            if (process != null && !process.HasExited)
            {
                process.Kill();
                LoadProcesses(); // 刷新进程列表
                StatusMessage = $"进程 {SelectedProcess.Name} 已结束";
                AddLogEntry("WARNING", $"进程 {SelectedProcess.Name} (PID: {SelectedProcess.Id}) 被用户结束");
                await _notificationService.ShowInfoAsync("信息", "进程已结束");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "结束进程失败");
            await _notificationService.ShowErrorAsync("错误", "结束进程失败");
        }
    }

    /// <summary>
    /// 启动选中服务
    /// </summary>
    [RelayCommand]
    private async Task StartSelectedService()
    {
        try
        {
            if (SelectedService == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择要启动的服务");
                return;
            }

            // 这里应该实现服务启动逻辑
            StatusMessage = $"服务 {SelectedService.Name} 启动请求已发送";
            AddLogEntry("INFO", $"尝试启动服务: {SelectedService.Name}");
            await _notificationService.ShowInfoAsync("信息", "服务启动请求已发送");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动服务失败");
            await _notificationService.ShowErrorAsync("错误", "启动服务失败");
        }
    }

    /// <summary>
    /// 停止选中服务
    /// </summary>
    [RelayCommand]
    private async Task StopSelectedService()
    {
        try
        {
            if (SelectedService == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择要停止的服务");
                return;
            }

            // 这里应该实现服务停止逻辑
            StatusMessage = $"服务 {SelectedService.Name} 停止请求已发送";
            AddLogEntry("WARNING", $"尝试停止服务: {SelectedService.Name}");
            await _notificationService.ShowInfoAsync("信息", "服务停止请求已发送");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止服务失败");
            await _notificationService.ShowErrorAsync("错误", "停止服务失败");
        }
    }

    /// <summary>
    /// 清除日志
    /// </summary>
    [RelayCommand]
    private void ClearLogs()
    {
        SystemLogs.Clear();
        StatusMessage = "系统日志已清除";
    }

    /// <summary>
    /// 导出系统报告
    /// </summary>
    [RelayCommand]
    private async Task ExportSystemReport()
    {
        try
        {
            // 这里应该实现系统报告导出逻辑
            var report = GenerateSystemReport();
            
            StatusMessage = "系统报告已生成";
            await _notificationService.ShowSuccessAsync("成功", "系统报告导出完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出系统报告失败");
            await _notificationService.ShowErrorAsync("错误", "导出系统报告失败");
        }
    }

    /// <summary>
    /// 设置更新间隔
    /// </summary>
    [RelayCommand]
    private void SetUpdateInterval(string interval)
    {
        if (int.TryParse(interval, out int newInterval) && newInterval >= 1000)
        {
            UpdateInterval = newInterval;
            if (IsMonitoring)
            {
                _updateTimer.Change(0, UpdateInterval);
            }
            StatusMessage = $"更新间隔已设置为 {newInterval} 毫秒";
        }
    }

    /// <summary>
    /// 初始化系统信息
    /// </summary>
    private void InitializeSystemInfo()
    {
        try
        {
            // 简化的内存信息获取
            var memInfo = GC.GetTotalMemory(false);
            TotalMemory = memInfo * 4; // 估算总内存
            
            SystemInfo = $"操作系统: {Environment.OSVersion}\n" +
                        $"处理器: {Environment.ProcessorCount} 核心\n" +
                        $"内存: {FormatBytes(TotalMemory)}\n" +
                        $"用户: {Environment.UserName}\n" +
                        $"机器名: {Environment.MachineName}";

            // 获取磁盘信息
            var drives = DriveInfo.GetDrives().Where(d => d.IsReady && d.DriveType == DriveType.Fixed);
            var systemDrive = drives.FirstOrDefault(d => d.Name == Path.GetPathRoot(Environment.SystemDirectory));
            if (systemDrive != null)
            {
                TotalDiskSpace = systemDrive.TotalSize;
                FreeDiskSpace = systemDrive.AvailableFreeSpace;
                DiskUsage = (double)(TotalDiskSpace - FreeDiskSpace) / TotalDiskSpace * 100;
            }

            // 获取GPU信息（简化实现）
            GpuInfo = "GPU信息获取中...";
            GetGpuInfo();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化系统信息失败");
            SystemInfo = "系统信息获取失败";
        }
    }

    /// <summary>
    /// 更新系统信息
    /// </summary>
    private void UpdateSystemInfo(object? state)
    {
        if (!IsMonitoring) return;

        try
        {
            // 更新CPU使用率
            CpuUsage = _cpuCounter.NextValue();

            // 更新内存使用率
            AvailableMemory = (long)_memoryCounter.NextValue() * 1024 * 1024; // 转换为字节
            UsedMemory = TotalMemory - AvailableMemory;
            MemoryUsage = (double)UsedMemory / TotalMemory * 100;

            // 更新网络状态（模拟）
            var random = new Random();
            NetworkUpload = random.NextDouble() * 100;
            NetworkDownload = random.NextDouble() * 500;

            // 更新GPU使用率（模拟）
            GpuUsage = random.NextDouble() * 100;

            LastUpdateTime = DateTime.Now;

            // 检查警告条件
            CheckWarningConditions();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新系统信息失败");
        }
    }

    /// <summary>
    /// 加载进程列表
    /// </summary>
    private void LoadProcesses()
    {
        try
        {
            Processes.Clear();
            var processes = Process.GetProcesses()
                                  .OrderByDescending(p => p.WorkingSet64)
                                  .Take(20); // 只显示前20个进程

            foreach (var process in processes)
            {
                try
                {
                    var processInfo = new ProcessInfo
                    {
                        Id = process.Id,
                        Name = process.ProcessName,
                        MemoryUsage = process.WorkingSet64,
                        CpuUsage = 0, // 需要额外计算
                        StartTime = process.StartTime,
                        Status = process.Responding ? "运行中" : "无响应"
                    };

                    Processes.Add(processInfo);
                }
                catch
                {
                    // 某些系统进程可能无法访问，忽略错误
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载进程列表失败");
        }
    }

    /// <summary>
    /// 加载服务列表
    /// </summary>
    private void LoadServices()
    {
        try
        {
            Services.Clear();
            
            // 添加一些示例服务
            var sampleServices = new[]
            {
                new ServiceInfo { Name = "MedicalImageAnalysis.Api", DisplayName = "医学影像分析API服务", Status = "运行中", StartType = "自动" },
                new ServiceInfo { Name = "MedicalImageAnalysis.Web", DisplayName = "医学影像分析Web服务", Status = "运行中", StartType = "自动" },
                new ServiceInfo { Name = "Windows Update", DisplayName = "Windows更新服务", Status = "运行中", StartType = "自动" },
                new ServiceInfo { Name = "Windows Defender", DisplayName = "Windows Defender防病毒服务", Status = "运行中", StartType = "自动" }
            };

            foreach (var service in sampleServices)
            {
                Services.Add(service);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载服务列表失败");
        }
    }

    /// <summary>
    /// 获取GPU信息
    /// </summary>
    private void GetGpuInfo()
    {
        try
        {
            // 简化的GPU信息获取
            GpuInfo = "NVIDIA GeForce RTX 4080 (示例)";
            GpuMemoryTotal = 16L * 1024 * 1024 * 1024; // 16GB
            GpuMemoryUsed = (long)(GpuMemoryTotal * 0.3); // 模拟30%使用率
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取GPU信息失败");
            GpuInfo = "GPU信息获取失败";
        }
    }

    /// <summary>
    /// 检查警告条件
    /// </summary>
    private void CheckWarningConditions()
    {
        // CPU使用率过高警告
        if (CpuUsage > 90)
        {
            AddLogEntry("WARNING", $"CPU使用率过高: {CpuUsage:F1}%");
        }

        // 内存使用率过高警告
        if (MemoryUsage > 85)
        {
            AddLogEntry("WARNING", $"内存使用率过高: {MemoryUsage:F1}%");
        }

        // 磁盘空间不足警告
        if (DiskUsage > 90)
        {
            AddLogEntry("WARNING", $"磁盘空间不足: {DiskUsage:F1}%");
        }
    }

    /// <summary>
    /// 添加日志条目
    /// </summary>
    private void AddLogEntry(string level, string message)
    {
        var logEntry = new LogEntry
        {
            Timestamp = DateTime.Now,
            Level = level,
            Message = message
        };

        // 在UI线程中添加日志
        System.Windows.Application.Current.Dispatcher.Invoke(() =>
        {
            SystemLogs.Insert(0, logEntry);
            
            // 限制日志数量
            while (SystemLogs.Count > 100)
            {
                SystemLogs.RemoveAt(SystemLogs.Count - 1);
            }
        });
    }

    /// <summary>
    /// 生成系统报告
    /// </summary>
    private string GenerateSystemReport()
    {
        var report = $"系统监控报告\n" +
                    $"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n\n" +
                    $"系统信息:\n{SystemInfo}\n\n" +
                    $"性能指标:\n" +
                    $"CPU使用率: {CpuUsage:F1}%\n" +
                    $"内存使用率: {MemoryUsage:F1}% ({FormatBytes(UsedMemory)}/{FormatBytes(TotalMemory)})\n" +
                    $"磁盘使用率: {DiskUsage:F1}% ({FormatBytes(TotalDiskSpace - FreeDiskSpace)}/{FormatBytes(TotalDiskSpace)})\n" +
                    $"GPU信息: {GpuInfo}\n" +
                    $"GPU使用率: {GpuUsage:F1}%\n\n" +
                    $"网络状态: {NetworkStatus}\n" +
                    $"上传速度: {NetworkUpload:F1} KB/s\n" +
                    $"下载速度: {NetworkDownload:F1} KB/s\n";

        return report;
    }

    /// <summary>
    /// 格式化字节数
    /// </summary>
    private string FormatBytes(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _updateTimer?.Dispose();
        _cpuCounter?.Dispose();
        _memoryCounter?.Dispose();
    }
}

/// <summary>
/// 进程信息模型
/// </summary>
public class ProcessInfo
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public long MemoryUsage { get; set; }
    public double CpuUsage { get; set; }
    public DateTime StartTime { get; set; }
    public string Status { get; set; } = "";
}

/// <summary>
/// 服务信息模型
/// </summary>
public class ServiceInfo
{
    public string Name { get; set; } = "";
    public string DisplayName { get; set; } = "";
    public string Status { get; set; } = "";
    public string StartType { get; set; } = "";
}

/// <summary>
/// 日志条目模型
/// </summary>
public class LogEntry
{
    public DateTime Timestamp { get; set; }
    public string Level { get; set; } = "";
    public string Message { get; set; } = "";
}
