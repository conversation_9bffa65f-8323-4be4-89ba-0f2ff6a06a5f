﻿#pragma checksum "..\..\..\..\Views\GdcmDicomViewer.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9A1B28126FC29C6E5C94FE97D0F06B14ED699204"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.Wpf.Views {
    
    
    /// <summary>
    /// GdcmDicomViewer
    /// </summary>
    public partial class GdcmDicomViewer : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFileButton;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WindowWidthTextBox;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WindowCenterTextBox;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetWindowButton;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportImageButton;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ImageScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image DicomImage;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView DicomInfoListView;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel StatisticsPanel;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MeanValueText;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MinValueText;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaxValueText;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StdDevText;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentWindowText;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ZoomLevelText;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageSizeText;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressText;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.Wpf;V1.0.0.0;component/views/gdcmdicomviewer.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OpenFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 23 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.OpenFileButton.Click += new System.Windows.RoutedEventHandler(this.OpenFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.WindowWidthTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 30 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.WindowWidthTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.WindowLevel_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.WindowCenterTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 35 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.WindowCenterTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.WindowLevel_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ResetWindowButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.ResetWindowButton.Click += new System.Windows.RoutedEventHandler(this.ResetWindowButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ExportImageButton = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.ExportImageButton.Click += new System.Windows.RoutedEventHandler(this.ExportImageButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ImageScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 7:
            this.DicomImage = ((System.Windows.Controls.Image)(target));
            return;
            case 8:
            this.DicomInfoListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 9:
            this.StatisticsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.MeanValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.MinValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.MaxValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.StdDevText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.CurrentWindowText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.ZoomLevelText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.ImageSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.ProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.ProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

