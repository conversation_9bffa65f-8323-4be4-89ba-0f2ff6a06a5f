﻿#pragma checksum "..\..\..\..\Views\GdcmDicomViewer.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CAF6F36040B862FDAFD38C97963F5B244DFBD786"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.Wpf.Views {
    
    
    /// <summary>
    /// GdcmDicomViewer
    /// </summary>
    public partial class GdcmDicomViewer : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFileButton;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WindowWidthTextBox;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WindowCenterTextBox;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetWindowButton;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ExposureSlider;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExposureValueText;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetExposureButton;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportImageButton;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ImageScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image DicomImage;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView DicomInfoListView;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel StatisticsPanel;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MeanValueText;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MinValueText;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaxValueText;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StdDevText;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentWindowText;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentExposureText;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ZoomLevelText;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageSizeText;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PixelRangeText;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressText;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Views\GdcmDicomViewer.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.Wpf;V1.0.0.0;component/views/gdcmdicomviewer.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OpenFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 23 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.OpenFileButton.Click += new System.Windows.RoutedEventHandler(this.OpenFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.WindowWidthTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 30 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.WindowWidthTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.WindowLevel_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.WindowCenterTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 35 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.WindowCenterTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.WindowLevel_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ResetWindowButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.ResetWindowButton.Click += new System.Windows.RoutedEventHandler(this.ResetWindowButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ExposureSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 49 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.ExposureSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.ExposureSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ExposureValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ResetExposureButton = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.ResetExposureButton.Click += new System.Windows.RoutedEventHandler(this.ResetExposureButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportImageButton = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\Views\GdcmDicomViewer.xaml"
            this.ExportImageButton.Click += new System.Windows.RoutedEventHandler(this.ExportImageButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ImageScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 10:
            this.DicomImage = ((System.Windows.Controls.Image)(target));
            return;
            case 11:
            this.DicomInfoListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 12:
            this.StatisticsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.MeanValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.MinValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.MaxValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.StdDevText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.CurrentWindowText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.CurrentExposureText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.ZoomLevelText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ImageSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.PixelRangeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.ProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.ProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

