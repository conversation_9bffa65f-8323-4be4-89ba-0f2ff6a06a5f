﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.text.json\9.0.7\buildTransitive\net8.0\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json\9.0.7\buildTransitive\net8.0\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)sqlitepclraw.lib.e_sqlite3\2.1.10\buildTransitive\net8.0\SQLitePCLRaw.lib.e_sqlite3.targets" Condition="Exists('$(NuGetPackageRoot)sqlitepclraw.lib.e_sqlite3\2.1.10\buildTransitive\net8.0\SQLitePCLRaw.lib.e_sqlite3.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\9.0.7\buildTransitive\net8.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\9.0.7\buildTransitive\net8.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.7\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.7\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)mono.texttemplating\3.0.0\buildTransitive\Mono.TextTemplating.targets" Condition="Exists('$(NuGetPackageRoot)mono.texttemplating\3.0.0\buildTransitive\Mono.TextTemplating.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ml.onnxruntime.managed\1.19.2\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Managed.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml.onnxruntime.managed\1.19.2\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Managed.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ml.onnxruntime.gpu.windows\1.19.2\buildTransitive\netstandard2.1\Microsoft.ML.OnnxRuntime.Gpu.Windows.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml.onnxruntime.gpu.windows\1.19.2\buildTransitive\netstandard2.1\Microsoft.ML.OnnxRuntime.Gpu.Windows.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ml.onnxruntime.gpu.linux\1.19.2\buildTransitive\netstandard2.1\Microsoft.ML.OnnxRuntime.Gpu.Linux.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml.onnxruntime.gpu.linux\1.19.2\buildTransitive\netstandard2.1\Microsoft.ML.OnnxRuntime.Gpu.Linux.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ml.onnxruntime.gpu\1.19.2\buildTransitive\netstandard2.1\Microsoft.ML.OnnxRuntime.Gpu.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml.onnxruntime.gpu\1.19.2\buildTransitive\netstandard2.1\Microsoft.ML.OnnxRuntime.Gpu.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ml.onnxruntime\1.19.2\build\netstandard2.1\Microsoft.ML.OnnxRuntime.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml.onnxruntime\1.19.2\build\netstandard2.1\Microsoft.ML.OnnxRuntime.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ml\3.0.1\build\netstandard2.0\Microsoft.ML.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml\3.0.1\build\netstandard2.0\Microsoft.ML.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.7\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.7\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.7\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.7\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.3.4\buildTransitive\Microsoft.CodeAnalysis.Analyzers.targets')" />
    <Import Project="$(NuGetPackageRoot)materialdesignthemes\5.0.0\build\MaterialDesignThemes.targets" Condition="Exists('$(NuGetPackageRoot)materialdesignthemes\5.0.0\build\MaterialDesignThemes.targets')" />
    <Import Project="$(NuGetPackageRoot)communitytoolkit.mvvm\8.2.2\buildTransitive\netstandard2.1\CommunityToolkit.Mvvm.targets" Condition="Exists('$(NuGetPackageRoot)communitytoolkit.mvvm\8.2.2\buildTransitive\netstandard2.1\CommunityToolkit.Mvvm.targets')" />
  </ImportGroup>
</Project>