# DICOM查看器功能改进

## 新增功能

### 1. 曝光值调整功能

#### 功能描述
- 添加了曝光值调整滑块，允许用户实时调整图像的曝光度
- 曝光值范围：-2.0 到 +2.0
- 步进值：0.1
- 实时预览调整效果

#### 使用方法
1. 在GDCM查看器工具栏中找到"曝光"滑块
2. 拖动滑块调整曝光值
3. 曝光值显示在滑块右侧
4. 点击"重置曝光"按钮可恢复到默认值0.0

#### 技术实现
- 曝光值通过指数函数计算：`exposureFactor = Math.Pow(2.0, exposure)`
- 调整窗宽：`adjustedWindowWidth = windowWidth / exposureFactor`
- 最终显示时应用曝光因子：`normalizedValue * exposureFactor`

### 2. 正确的DICOM像素值范围处理

#### 功能描述
- 支持医院标准的DICOM像素值范围：-1024 到 3071 HU (Hounsfield Units)
- 正确处理重缩放参数（Rescale Slope 和 Rescale Intercept）
- 将原始像素值转换为Hounsfield单位进行处理

#### 技术实现
- 获取DICOM文件中的重缩放参数
- 转换公式：`HU = rescaleSlope * rawValue + rescaleIntercept`
- 像素值范围限制：`Math.Clamp(huValue, -1024, 3071)`

### 3. 界面改进

#### 新增控件
- 曝光值调整滑块
- 曝光值数值显示
- 重置曝光按钮
- 像素范围信息显示

#### 信息显示
- 当前曝光值：显示在信息面板中
- 像素范围：显示"像素范围: -1024 ~ 3071 HU"
- 实时更新当前设置信息

## 代码修改说明

### 1. GdcmDicomViewer.xaml
- 添加曝光值调整控件到工具栏
- 添加曝光值显示到信息面板

### 2. GdcmDicomViewer.xaml.cs
- 添加曝光值相关字段和属性
- 实现曝光值调整事件处理
- 添加曝光值重置功能
- 更新设置显示方法

### 3. GdcmImageProcessor.cs
- 添加支持曝光值调整的新方法
- 实现正确的DICOM像素值范围处理
- 支持Hounsfield单位转换
- 保持向后兼容性

## 使用场景

### 1. 图像太暗的情况
- 增加曝光值（正值）可以提亮图像
- 适用于低对比度的医学影像

### 2. 图像过亮的情况
- 减少曝光值（负值）可以降低亮度
- 适用于高密度组织的观察

### 3. 精细调整
- 小幅度调整曝光值可以优化图像显示效果
- 配合窗宽窗位调整实现最佳观察效果

## 技术特点

### 1. 实时处理
- 曝光值调整实时生效
- 无需重新加载DICOM文件

### 2. 医学标准
- 遵循DICOM标准的像素值处理
- 支持Hounsfield单位

### 3. 用户友好
- 直观的滑块控制
- 实时数值显示
- 一键重置功能

## 注意事项

1. 曝光值调整不会修改原始DICOM数据
2. 调整仅影响显示效果，不影响医学测量
3. 建议配合窗宽窗位调整使用以获得最佳效果
4. 像素值范围限制确保了医学影像的合理性

## 测试建议

1. 使用不同类型的DICOM文件测试
2. 验证曝光值调整的效果
3. 确认像素值范围处理的正确性
4. 测试重置功能的可靠性
