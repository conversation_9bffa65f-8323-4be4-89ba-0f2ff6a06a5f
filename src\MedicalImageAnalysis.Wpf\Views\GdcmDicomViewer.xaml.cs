using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using MedicalImageAnalysis.Wpf.Services;

namespace MedicalImageAnalysis.Wpf.Views
{
    /// <summary>
    /// 基于GDCM的DICOM查看器用户控件
    /// </summary>
    public partial class GdcmDicomViewer : UserControl
    {
        private readonly ILogger<GdcmDicomViewer> _logger;
        private readonly GdcmDicomService _gdcmDicomService;
        private readonly GdcmImageProcessor _gdcmImageProcessor;
        
        private string? _currentDicomFile;
        private double _originalWindowCenter = 40;
        private double _originalWindowWidth = 400;
        private double _currentExposure = 0.0;
        private BitmapSource? _currentImage;
        private BitmapSource? _originalImage;

        public GdcmDicomViewer()
        {
            InitializeComponent();
            
            // 从依赖注入容器获取服务
            var serviceProvider = ((App)System.Windows.Application.Current).ServiceProvider;
            _logger = serviceProvider?.GetService<ILogger<GdcmDicomViewer>>() 
                     ?? throw new InvalidOperationException("无法获取日志服务");
            
            _gdcmDicomService = new GdcmDicomService(
                serviceProvider.GetService<ILogger<GdcmDicomService>>()!);
            
            _gdcmImageProcessor = new GdcmImageProcessor(
                serviceProvider.GetService<ILogger<GdcmImageProcessor>>()!);

            InitializeViewer();
        }

        /// <summary>
        /// 初始化查看器
        /// </summary>
        private void InitializeViewer()
        {
            try
            {
                UpdateStatus("GDCM DICOM查看器已就绪");
                UpdateCurrentSettings();
                _logger.LogInformation("GDCM DICOM查看器初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化GDCM DICOM查看器失败");
                UpdateStatus($"初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 打开DICOM文件按钮点击事件
        /// </summary>
        private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择DICOM文件",
                    Filter = "DICOM文件 (*.dcm)|*.dcm|所有文件 (*.*)|*.*",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    await LoadDicomFileAsync(openFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开DICOM文件失败");
                MessageBox.Show($"打开文件失败: {ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载DICOM文件
        /// </summary>
        private async Task LoadDicomFileAsync(string filePath)
        {
            try
            {
                ShowProgress("正在加载DICOM文件...");
                UpdateStatus($"正在加载: {Path.GetFileName(filePath)}");

                // 验证文件
                if (!await _gdcmDicomService.IsValidDicomFileAsync(filePath))
                {
                    throw new InvalidOperationException("不是有效的DICOM文件");
                }

                _currentDicomFile = filePath;

                // 解析DICOM元数据
                var dicomInstance = await _gdcmDicomService.ParseDicomFileAsync(filePath);
                
                // 获取原始窗宽窗位
                _originalWindowCenter = dicomInstance.WindowCenter;
                _originalWindowWidth = dicomInstance.WindowWidth;

                // 更新窗宽窗位输入框
                WindowCenterTextBox.Text = _originalWindowCenter.ToString("F0");
                WindowWidthTextBox.Text = _originalWindowWidth.ToString("F0");

                // 提取并显示图像
                _originalImage = await _gdcmDicomService.ExtractImageAsync(filePath);
                _currentImage = _originalImage;
                if (_currentImage != null)
                {
                    DicomImage.Source = _currentImage;
                    UpdateImageInfo();
                }

                // 重置曝光值
                _currentExposure = 0.0;
                ExposureSlider.Value = 0.0;
                ExposureValueText.Text = "0.0";

                // 显示DICOM信息
                await DisplayDicomInfoAsync(filePath);

                // 显示图像统计信息
                await DisplayImageStatisticsAsync(filePath);

                UpdateStatus($"已加载: {Path.GetFileName(filePath)} ({dicomInstance.Columns}x{dicomInstance.Rows})");
                UpdateCurrentSettings();

                _logger.LogInformation("DICOM文件加载完成: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载DICOM文件失败: {FilePath}", filePath);
                UpdateStatus($"加载失败: {ex.Message}");
                throw;
            }
            finally
            {
                HideProgress();
            }
        }

        /// <summary>
        /// 显示DICOM信息
        /// </summary>
        private async Task DisplayDicomInfoAsync(string filePath)
        {
            try
            {
                var dicomInfo = await _gdcmDicomService.GetDicomInfoAsync(filePath);
                var displayItems = dicomInfo.Select(kvp => new { Key = kvp.Key, Value = kvp.Value?.ToString() ?? "N/A" }).ToList();
                DicomInfoListView.ItemsSource = displayItems;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "显示DICOM信息失败");
            }
        }

        /// <summary>
        /// 显示图像统计信息
        /// </summary>
        private async Task DisplayImageStatisticsAsync(string filePath)
        {
            try
            {
                var stats = await _gdcmImageProcessor.GetImageStatisticsAsync(filePath);
                
                MeanValueText.Text = $"平均值: {stats.Mean:F2}";
                MinValueText.Text = $"最小值: {stats.Min:F2}";
                MaxValueText.Text = $"最大值: {stats.Max:F2}";
                StdDevText.Text = $"标准差: {stats.StandardDeviation:F2}";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "显示图像统计信息失败");
                MeanValueText.Text = "统计信息不可用";
                MinValueText.Text = "";
                MaxValueText.Text = "";
                StdDevText.Text = "";
            }
        }

        /// <summary>
        /// 窗宽窗位文本框变化事件
        /// </summary>
        private async void WindowLevel_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_currentDicomFile == null) return;

            try
            {
                if (double.TryParse(WindowCenterTextBox.Text, out double windowCenter) &&
                    double.TryParse(WindowWidthTextBox.Text, out double windowWidth))
                {
                    await ApplyWindowLevelAsync(windowCenter, windowWidth);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "应用窗宽窗位失败");
            }
        }

        /// <summary>
        /// 应用窗宽窗位
        /// </summary>
        private async Task ApplyWindowLevelAsync(double windowCenter, double windowWidth)
        {
            if (_currentDicomFile == null) return;

            try
            {
                var adjustedImage = await _gdcmImageProcessor.ApplyWindowLevelAsync(
                    _currentDicomFile, windowCenter, windowWidth);
                
                if (adjustedImage != null)
                {
                    DicomImage.Source = adjustedImage;
                    _currentImage = adjustedImage;
                    UpdateCurrentSettings();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用窗宽窗位失败");
            }
        }

        /// <summary>
        /// 重置窗宽窗位按钮点击事件
        /// </summary>
        private async void ResetWindowButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                WindowCenterTextBox.Text = _originalWindowCenter.ToString("F0");
                WindowWidthTextBox.Text = _originalWindowWidth.ToString("F0");

                if (_currentDicomFile != null)
                {
                    await ApplyWindowLevelAsync(_originalWindowCenter, _originalWindowWidth);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置窗宽窗位失败");
            }
        }

        /// <summary>
        /// 曝光值滑块变化事件
        /// </summary>
        private async void ExposureSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (!IsLoaded || _currentDicomFile == null) return;

            _currentExposure = e.NewValue;
            ExposureValueText.Text = _currentExposure.ToString("F1");

            await ApplyExposureAdjustmentAsync();
        }

        /// <summary>
        /// 重置曝光值按钮点击事件
        /// </summary>
        private async void ResetExposureButton_Click(object sender, RoutedEventArgs e)
        {
            ExposureSlider.Value = 0.0;
            _currentExposure = 0.0;
            ExposureValueText.Text = "0.0";

            await ApplyExposureAdjustmentAsync();
        }

        /// <summary>
        /// 导出图像按钮点击事件
        /// </summary>
        private async void ExportImageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentDicomFile == null || _currentImage == null)
            {
                MessageBox.Show("请先加载DICOM文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出图像",
                    Filter = "PNG文件 (*.png)|*.png|JPEG文件 (*.jpg)|*.jpg|BMP文件 (*.bmp)|*.bmp|TIFF文件 (*.tiff)|*.tiff",
                    DefaultExt = "png",
                    FileName = Path.GetFileNameWithoutExtension(_currentDicomFile) + "_export"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ShowProgress("正在导出图像...");
                    
                    var extension = Path.GetExtension(saveFileDialog.FileName).ToUpper();
                    var format = extension switch
                    {
                        ".PNG" => "PNG",
                        ".JPG" or ".JPEG" => "JPEG",
                        ".BMP" => "BMP",
                        ".TIFF" or ".TIF" => "TIFF",
                        _ => "PNG"
                    };

                    var success = await _gdcmImageProcessor.ConvertDicomToImageAsync(
                        _currentDicomFile, saveFileDialog.FileName, format);

                    if (success)
                    {
                        UpdateStatus($"图像已导出: {Path.GetFileName(saveFileDialog.FileName)}");
                        MessageBox.Show("图像导出成功!", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        throw new InvalidOperationException("图像导出失败");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出图像失败");
                MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                HideProgress();
            }
        }

        /// <summary>
        /// 更新图像信息显示
        /// </summary>
        private void UpdateImageInfo()
        {
            if (_currentImage != null)
            {
                ImageSizeText.Text = $"图像尺寸: {_currentImage.PixelWidth}x{_currentImage.PixelHeight}";
            }
        }

        /// <summary>
        /// 应用曝光值调整
        /// </summary>
        private async Task ApplyExposureAdjustmentAsync()
        {
            if (_currentDicomFile == null || _originalImage == null) return;

            try
            {
                // 获取当前窗宽窗位
                if (double.TryParse(WindowCenterTextBox.Text, out double windowCenter) &&
                    double.TryParse(WindowWidthTextBox.Text, out double windowWidth))
                {
                    // 应用曝光值调整的窗宽窗位
                    var adjustedImage = await _gdcmImageProcessor.ApplyWindowLevelWithExposureAsync(
                        _currentDicomFile, windowCenter, windowWidth, _currentExposure);

                    if (adjustedImage != null)
                    {
                        DicomImage.Source = adjustedImage;
                        _currentImage = adjustedImage;
                        UpdateCurrentSettings();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用曝光值调整失败");
            }
        }

        /// <summary>
        /// 更新当前设置显示
        /// </summary>
        private void UpdateCurrentSettings()
        {
            if (double.TryParse(WindowCenterTextBox.Text, out double wc) &&
                double.TryParse(WindowWidthTextBox.Text, out double ww))
            {
                CurrentWindowText.Text = $"窗宽/窗位: {ww:F0}/{wc:F0}";
            }

            CurrentExposureText.Text = $"曝光值: {_currentExposure:F1}";

            // WPF ScrollViewer没有ZoomFactor属性，使用固定值
            ZoomLevelText.Text = "缩放: 100%";

            // 显示像素值范围
            PixelRangeText.Text = "像素范围: -1024 ~ 3071 HU";
        }

        /// <summary>
        /// 更新状态栏
        /// </summary>
        private void UpdateStatus(string message)
        {
            StatusText.Text = message;
        }

        /// <summary>
        /// 显示进度条
        /// </summary>
        private void ShowProgress(string message)
        {
            ProgressText.Text = message;
            ProgressBar.Visibility = Visibility.Visible;
            ProgressBar.IsIndeterminate = true;
        }

        /// <summary>
        /// 隐藏进度条
        /// </summary>
        private void HideProgress()
        {
            ProgressText.Text = "";
            ProgressBar.Visibility = Visibility.Collapsed;
            ProgressBar.IsIndeterminate = false;
        }
    }
}
