using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Core.Models;

/// <summary>
/// DICOM解析结果
/// </summary>
public class DicomParseResult
{
    /// <summary>
    /// 是否解析成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 解析得到的DICOM实例
    /// </summary>
    public DicomInstance? Instance { get; set; }

    /// <summary>
    /// 验证结果
    /// </summary>
    public DicomValidationResult? ValidationResult { get; set; }
}

/// <summary>
/// DICOM验证结果
/// </summary>
public class DicomValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 是否包含像素数据
    /// </summary>
    public bool HasPixelData { get; set; }

    /// <summary>
    /// SOP类UID
    /// </summary>
    public string? SopClassUid { get; set; }

    /// <summary>
    /// 传输语法UID
    /// </summary>
    public string? TransferSyntaxUid { get; set; }
}

/// <summary>
/// 像素数据
/// </summary>
public class PixelData
{
    /// <summary>
    /// 像素数据数组
    /// </summary>
    public Array Data { get; set; } = null!;

    /// <summary>
    /// 数据类型
    /// </summary>
    public Type DataType { get; set; } = typeof(ushort);

    /// <summary>
    /// 图像宽度
    /// </summary>
    public int Width { get; set; }

    /// <summary>
    /// 图像高度
    /// </summary>
    public int Height { get; set; }

    /// <summary>
    /// 每像素位数
    /// </summary>
    public int BitsPerPixel { get; set; }

    /// <summary>
    /// 是否为有符号数据
    /// </summary>
    public bool IsSigned { get; set; }

    /// <summary>
    /// 光度解释
    /// </summary>
    public string PhotometricInterpretation { get; set; } = string.Empty;

    /// <summary>
    /// 获取指定位置的像素值
    /// </summary>
    public T GetPixel<T>(int x, int y) where T : struct
    {
        if (x < 0 || x >= Width || y < 0 || y >= Height)
            throw new ArgumentOutOfRangeException();

        var index = y * Width + x;
        if (Data is T[] typedArray)
        {
            return typedArray[index];
        }

        return (T)Data.GetValue(index)!;
    }

    /// <summary>
    /// 设置指定位置的像素值
    /// </summary>
    public void SetPixel<T>(int x, int y, T value) where T : struct
    {
        if (x < 0 || x >= Width || y < 0 || y >= Height)
            throw new ArgumentOutOfRangeException();

        var index = y * Width + x;
        if (Data is T[] typedArray)
        {
            typedArray[index] = value;
        }
        else
        {
            Data.SetValue(value, index);
        }
    }
}

/// <summary>
/// 批量处理结果
/// </summary>
public class BatchProcessResult
{
    /// <summary>
    /// 总处理数量
    /// </summary>
    public int TotalProcessed { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 成功处理的文件列表
    /// </summary>
    public List<string> SuccessfulFiles { get; set; } = new();

    /// <summary>
    /// 失败的文件及错误信息
    /// </summary>
    public Dictionary<string, string> FailedFiles { get; set; } = new();

    /// <summary>
    /// 处理成功率
    /// </summary>
    public double SuccessRate => TotalProcessed > 0 ? (double)SuccessCount / TotalProcessed : 0.0;
}

/// <summary>
/// 图像格式枚举
/// </summary>
public enum ImageFormat
{
    Png = 1,
    Jpeg = 2,
    Bmp = 3,
    Tiff = 4
}

/// <summary>
/// DICOM元数据
/// </summary>
public class DicomMetadata
{
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string PatientName { get; set; } = string.Empty;

    /// <summary>
    /// 患者ID
    /// </summary>
    public string PatientId { get; set; } = string.Empty;

    /// <summary>
    /// 患者性别
    /// </summary>
    public string PatientSex { get; set; } = string.Empty;

    /// <summary>
    /// 患者出生日期
    /// </summary>
    public DateTime? PatientBirthDate { get; set; }

    /// <summary>
    /// 研究实例UID
    /// </summary>
    public string StudyInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// 研究日期
    /// </summary>
    public DateTime? StudyDate { get; set; }

    /// <summary>
    /// 研究时间
    /// </summary>
    public TimeSpan? StudyTime { get; set; }

    /// <summary>
    /// 研究描述
    /// </summary>
    public string StudyDescription { get; set; } = string.Empty;

    /// <summary>
    /// 序列实例UID
    /// </summary>
    public string SeriesInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// 序列号
    /// </summary>
    public int SeriesNumber { get; set; }

    /// <summary>
    /// 序列描述
    /// </summary>
    public string SeriesDescription { get; set; } = string.Empty;

    /// <summary>
    /// 模态
    /// </summary>
    public string Modality { get; set; } = string.Empty;

    /// <summary>
    /// SOP实例UID
    /// </summary>
    public string SopInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// 实例号
    /// </summary>
    public int InstanceNumber { get; set; }

    /// <summary>
    /// 图像行数
    /// </summary>
    public int Rows { get; set; }

    /// <summary>
    /// 图像列数
    /// </summary>
    public int Columns { get; set; }

    /// <summary>
    /// 像素间距
    /// </summary>
    public (double X, double Y) PixelSpacing { get; set; }

    /// <summary>
    /// 切片厚度
    /// </summary>
    public double SliceThickness { get; set; }

    /// <summary>
    /// 切片位置
    /// </summary>
    public double SliceLocation { get; set; }

    /// <summary>
    /// 图像位置
    /// </summary>
    public (double X, double Y, double Z) ImagePosition { get; set; }

    /// <summary>
    /// 图像方向
    /// </summary>
    public double[] ImageOrientationPatient { get; set; } = new double[6];

    /// <summary>
    /// 窗宽
    /// </summary>
    public double WindowWidth { get; set; }

    /// <summary>
    /// 窗位
    /// </summary>
    public double WindowCenter { get; set; }

    /// <summary>
    /// 重缩放斜率
    /// </summary>
    public double RescaleSlope { get; set; } = 1.0;

    /// <summary>
    /// 重缩放截距
    /// </summary>
    public double RescaleIntercept { get; set; } = 0.0;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 图像坐标信息
/// </summary>
public class ImageCoordinateInfo
{
    /// <summary>
    /// 图像位置（患者坐标系）
    /// </summary>
    public (double X, double Y, double Z) ImagePosition { get; set; }

    /// <summary>
    /// 图像方向余弦（患者坐标系）
    /// </summary>
    public double[] ImageOrientationPatient { get; set; } = new double[6];

    /// <summary>
    /// 像素间距 (行间距, 列间距)
    /// </summary>
    public (double Row, double Column) PixelSpacing { get; set; }

    /// <summary>
    /// 切片厚度
    /// </summary>
    public double SliceThickness { get; set; }

    /// <summary>
    /// 切片位置
    /// </summary>
    public double SliceLocation { get; set; }

    /// <summary>
    /// 图像行数
    /// </summary>
    public int Rows { get; set; }

    /// <summary>
    /// 图像列数
    /// </summary>
    public int Columns { get; set; }

    /// <summary>
    /// 将像素坐标转换为患者坐标
    /// </summary>
    /// <param name="pixelRow">像素行</param>
    /// <param name="pixelColumn">像素列</param>
    /// <returns>患者坐标</returns>
    public (double X, double Y, double Z) PixelToPatientCoordinate(int pixelRow, int pixelColumn)
    {
        // 计算像素在图像坐标系中的位置
        var deltaI = pixelColumn * PixelSpacing.Column;
        var deltaJ = pixelRow * PixelSpacing.Row;

        // 转换到患者坐标系
        var x = ImagePosition.X + deltaI * ImageOrientationPatient[0] + deltaJ * ImageOrientationPatient[3];
        var y = ImagePosition.Y + deltaI * ImageOrientationPatient[1] + deltaJ * ImageOrientationPatient[4];
        var z = ImagePosition.Z + deltaI * ImageOrientationPatient[2] + deltaJ * ImageOrientationPatient[5];

        return (x, y, z);
    }

    /// <summary>
    /// 将患者坐标转换为像素坐标
    /// </summary>
    /// <param name="patientX">患者X坐标</param>
    /// <param name="patientY">患者Y坐标</param>
    /// <param name="patientZ">患者Z坐标</param>
    /// <returns>像素坐标</returns>
    public (int Row, int Column) PatientToPixelCoordinate(double patientX, double patientY, double patientZ)
    {
        // 计算相对于图像原点的偏移
        var deltaX = patientX - ImagePosition.X;
        var deltaY = patientY - ImagePosition.Y;
        var deltaZ = patientZ - ImagePosition.Z;

        // 使用图像方向余弦的逆变换
        // 这是一个简化的实现，实际应该使用矩阵逆变换
        var deltaI = (deltaX * ImageOrientationPatient[0] + deltaY * ImageOrientationPatient[1] + deltaZ * ImageOrientationPatient[2]) / PixelSpacing.Column;
        var deltaJ = (deltaX * ImageOrientationPatient[3] + deltaY * ImageOrientationPatient[4] + deltaZ * ImageOrientationPatient[5]) / PixelSpacing.Row;

        var pixelColumn = (int)Math.Round(deltaI);
        var pixelRow = (int)Math.Round(deltaJ);

        return (pixelRow, pixelColumn);
    }

    /// <summary>
    /// 获取体素的物理尺寸
    /// </summary>
    /// <returns>体素尺寸 (宽度, 高度, 深度)</returns>
    public (double Width, double Height, double Depth) GetVoxelSize()
    {
        return (PixelSpacing.Column, PixelSpacing.Row, SliceThickness);
    }

    /// <summary>
    /// 计算两个像素点之间的物理距离
    /// </summary>
    /// <param name="row1">第一个点的行</param>
    /// <param name="col1">第一个点的列</param>
    /// <param name="row2">第二个点的行</param>
    /// <param name="col2">第二个点的列</param>
    /// <returns>物理距离（毫米）</returns>
    public double CalculatePixelDistance(int row1, int col1, int row2, int col2)
    {
        var coord1 = PixelToPatientCoordinate(row1, col1);
        var coord2 = PixelToPatientCoordinate(row2, col2);

        var dx = coord2.X - coord1.X;
        var dy = coord2.Y - coord1.Y;
        var dz = coord2.Z - coord1.Z;

        return Math.Sqrt(dx * dx + dy * dy + dz * dz);
    }

    /// <summary>
    /// 计算像素区域的物理面积
    /// </summary>
    /// <param name="pixelCount">像素数量</param>
    /// <returns>物理面积（平方毫米）</returns>
    public double CalculatePixelArea(int pixelCount)
    {
        return pixelCount * PixelSpacing.Row * PixelSpacing.Column;
    }

    /// <summary>
    /// 计算体素的物理体积
    /// </summary>
    /// <param name="voxelCount">体素数量</param>
    /// <returns>物理体积（立方毫米）</returns>
    public double CalculateVoxelVolume(int voxelCount)
    {
        return voxelCount * PixelSpacing.Row * PixelSpacing.Column * SliceThickness;
    }
}
