using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Services;
using MedicalImageAnalysis.Core.Data;
using Microsoft.EntityFrameworkCore;

namespace MedicalImageAnalysis.Wpf.Views
{
    /// <summary>
    /// DicomUploadView.xaml 的交互逻辑
    /// </summary>
    public partial class DicomUploadView : System.Windows.Controls.UserControl
    {
        private List<System.IO.FileInfo> _selectedFiles = new();
        private readonly HttpClient _httpClient;
        private readonly ILogger<DicomUploadView> _logger;
        private readonly ISimpleDicomService _dicomService;
        private readonly IDatabaseService _databaseService;
        private const string API_BASE_URL = "http://localhost:5000/api";
        private bool _isOfflineMode = false;

        public DicomUploadView()
        {
            InitializeComponent();
            _httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(30) };
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<DicomUploadView>.Instance;

            // 初始化数据库服务
            var options = new DbContextOptionsBuilder<MedicalImageDbContext>()
                .UseSqlite("Data Source=medical_images.db")
                .Options;
            var dbContext = new MedicalImageDbContext(options);
            _databaseService = new DatabaseService(dbContext, Microsoft.Extensions.Logging.Abstractions.NullLogger<DatabaseService>.Instance);
            _dicomService = new SimpleDicomService(Microsoft.Extensions.Logging.Abstractions.NullLogger<SimpleDicomService>.Instance, _databaseService);

            // 确保数据库存在
            InitializeDatabaseAsync();

            // 检查API连接状态
            CheckApiConnection();
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        private async void InitializeDatabaseAsync()
        {
            try
            {
                if (!await _databaseService.DatabaseExistsAsync())
                {
                    await _databaseService.CreateDatabaseAsync();
                    _logger.LogInformation("数据库创建成功");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化数据库失败");
            }
        }

        /// <summary>
        /// 拖拽进入事件
        /// </summary>
        private void DropZone_DragOver(object sender, System.Windows.DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                e.Effects = DragDropEffects.Copy;
                DropZone.Background = System.Windows.Media.Brushes.LightBlue;
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
            e.Handled = true;
        }

        /// <summary>
        /// 拖拽离开事件
        /// </summary>
        private void DropZone_DragLeave(object sender, System.Windows.DragEventArgs e)
        {
            DropZone.Background = System.Windows.Media.Brushes.Transparent;
        }

        /// <summary>
        /// 文件拖拽放置事件
        /// </summary>
        private void DropZone_Drop(object sender, System.Windows.DragEventArgs e)
        {
            DropZone.Background = System.Windows.Media.Brushes.Transparent;
            
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                AddFiles(files);
            }
        }

        /// <summary>
        /// 选择文件按钮点击事件
        /// </summary>
        private void SelectFiles_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择 DICOM 文件",
                Filter = "DICOM 文件 (*.dcm;*.dicom)|*.dcm;*.dicom|所有文件 (*.*)|*.*",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                AddFiles(openFileDialog.FileNames);
            }
        }

        /// <summary>
        /// 添加文件到列表
        /// </summary>
        private void AddFiles(string[] filePaths)
        {
            foreach (string filePath in filePaths)
            {
                var fileInfo = new System.IO.FileInfo(filePath);
                
                // 检查文件扩展名
                if (fileInfo.Extension.ToLower() == ".dcm" || 
                    fileInfo.Extension.ToLower() == ".dicom" ||
                    string.IsNullOrEmpty(fileInfo.Extension))
                {
                    // 检查是否已存在
                    if (!_selectedFiles.Any(f => f.FullName == fileInfo.FullName))
                    {
                        _selectedFiles.Add(fileInfo);
                    }
                }
            }

            UpdateFileList();
        }

        /// <summary>
        /// 检查API连接状态
        /// </summary>
        private async void CheckApiConnection()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{API_BASE_URL}/health",
                    new CancellationTokenSource(TimeSpan.FromSeconds(5)).Token);
                _isOfflineMode = !response.IsSuccessStatusCode;
            }
            catch
            {
                _isOfflineMode = true;
            }
        }

        /// <summary>
        /// 更新文件列表显示
        /// </summary>
        private void UpdateFileList()
        {
            var fileDisplayList = _selectedFiles.Select(f => new
            {
                Name = f.Name,
                Size = FormatFileSize(f.Length),
                FullPath = f.FullName
            }).ToList();

            FileListView.ItemsSource = fileDisplayList;

            // 显示或隐藏文件列表卡片
            FileListCard.Visibility = _selectedFiles.Any() ? Visibility.Visible : Visibility.Collapsed;
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 清空文件列表
        /// </summary>
        private void ClearFiles_Click(object sender, RoutedEventArgs e)
        {
            _selectedFiles.Clear();
            FileListView.ItemsSource = null;
            FileListCard.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 开始处理文件
        /// </summary>
        private async void StartProcessing_Click(object sender, RoutedEventArgs e)
        {
            if (!_selectedFiles.Any())
            {
                MessageBox.Show("请先选择要处理的 DICOM 文件。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 显示处理进度
            ProcessingCard.Visibility = Visibility.Visible;
            ProcessingProgressBar.IsIndeterminate = true;
            ProcessingStatusText.Text = "正在处理 DICOM 文件...";

            try
            {
                var results = new List<string>();
                int processedCount = 0;

                if (_isOfflineMode)
                {
                    results.Add("⚠️ 离线模式 - 仅进行本地文件验证");
                    results.Add("");
                }

                foreach (var file in _selectedFiles)
                {
                    ProcessingStatusText.Text = $"正在处理文件 {processedCount + 1}/{_selectedFiles.Count}: {file.Name}";

                    if (_isOfflineMode)
                    {
                        // 离线模式：仅进行基本文件验证
                        var offlineResult = ValidateDicomFileOffline(file);
                        if (offlineResult.IsValid)
                        {
                            results.Add($"✓ {file.Name}: 文件格式验证通过");
                            results.Add($"  - 文件大小: {FormatFileSize(file.Length)}");
                            results.Add($"  - 修改时间: {file.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
                        }
                        else
                        {
                            results.Add($"✗ {file.Name}: {offlineResult.ErrorMessage}");
                        }
                    }
                    else
                    {
                        // 在线模式：完整验证和元数据提取
                        var validationResult = await ValidateDicomFileAsync(file);
                        if (validationResult != null)
                        {
                            results.Add($"✓ {file.Name}: 验证成功");

                            // 提取元数据
                            var metadata = await ExtractMetadataAsync(file);
                            if (metadata != null)
                            {
                                results.Add($"  - 患者: {metadata.PatientName}");
                                results.Add($"  - 检查日期: {metadata.StudyDate}");
                                results.Add($"  - 模态: {metadata.Modality}");
                            }
                        }
                        else
                        {
                            results.Add($"✗ {file.Name}: 验证失败");
                        }
                    }

                    processedCount++;

                    // 更新进度
                    ProcessingProgressBar.IsIndeterminate = false;
                    ProcessingProgressBar.Value = (double)processedCount / _selectedFiles.Count * 100;
                }

                ProcessingStatusText.Text = "处理完成！";

                // 显示结果
                var resultText = string.Join("\n", results);
                var resultWindow = new Window
                {
                    Title = "处理结果",
                    Width = 600,
                    Height = 400,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    Content = new ScrollViewer
                    {
                        Content = new TextBlock
                        {
                            Text = resultText,
                            Margin = new Thickness(16),
                            TextWrapping = TextWrapping.Wrap,
                            FontFamily = new System.Windows.Media.FontFamily("Consolas")
                        }
                    }
                };
                resultWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理 DICOM 文件时发生错误");
                MessageBox.Show($"处理过程中发生错误：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 隐藏处理进度
                ProcessingCard.Visibility = Visibility.Collapsed;
                ProcessingProgressBar.Value = 0;
                ProcessingProgressBar.IsIndeterminate = false;
            }
        }

        /// <summary>
        /// 离线验证 DICOM 文件
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateDicomFileOffline(System.IO.FileInfo file)
        {
            try
            {
                // 检查文件是否存在
                if (!file.Exists)
                {
                    return (false, "文件不存在");
                }

                // 检查文件大小
                if (file.Length == 0)
                {
                    return (false, "文件为空");
                }

                if (file.Length > 500 * 1024 * 1024) // 500MB
                {
                    return (false, "文件过大（超过500MB）");
                }

                // 检查文件扩展名
                var extension = file.Extension.ToLower();
                if (extension != ".dcm" && extension != ".dicom" && !string.IsNullOrEmpty(extension))
                {
                    return (false, "不支持的文件格式");
                }

                // 简单的DICOM文件头检查
                using var stream = file.OpenRead();
                var buffer = new byte[132];
                var bytesRead = stream.Read(buffer, 0, 132);

                if (bytesRead >= 132)
                {
                    // 检查DICOM前缀（位置128-131应该是"DICM"）
                    var dicmSignature = System.Text.Encoding.ASCII.GetString(buffer, 128, 4);
                    if (dicmSignature == "DICM")
                    {
                        return (true, "");
                    }
                }

                // 如果没有DICM签名，可能是隐式DICOM文件，仍然认为有效
                return (true, "");
            }
            catch (Exception ex)
            {
                return (false, $"验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证 DICOM 文件
        /// </summary>
        private async Task<dynamic?> ValidateDicomFileAsync(System.IO.FileInfo file)
        {
            try
            {
                using var content = new MultipartFormDataContent();
                using var fileStream = file.OpenRead();
                using var streamContent = new StreamContent(fileStream);
                streamContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");
                content.Add(streamContent, "file", file.Name);

                var response = await _httpClient.PostAsync($"{API_BASE_URL}/study/validate", content);
                if (response.IsSuccessStatusCode)
                {
                    var jsonResult = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject(jsonResult);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证 DICOM 文件失败: {FileName}", file.Name);
                return null;
            }
        }

        /// <summary>
        /// 提取 DICOM 元数据
        /// </summary>
        private async Task<DicomMetadata?> ExtractMetadataAsync(System.IO.FileInfo file)
        {
            try
            {
                using var content = new MultipartFormDataContent();
                using var fileStream = file.OpenRead();
                using var streamContent = new StreamContent(fileStream);
                streamContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");
                content.Add(streamContent, "file", file.Name);

                var response = await _httpClient.PostAsync($"{API_BASE_URL}/study/metadata", content);
                if (response.IsSuccessStatusCode)
                {
                    var jsonResult = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<DicomMetadata>(jsonResult);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取 DICOM 元数据失败: {FileName}", file.Name);
                return null;
            }
        }

        /// <summary>
        /// DICOM 元数据模型
        /// </summary>
        public class DicomMetadata
        {
            public string PatientName { get; set; } = "";
            public string PatientID { get; set; } = "";
            public string StudyDate { get; set; } = "";
            public string StudyTime { get; set; } = "";
            public string Modality { get; set; } = "";
            public string SeriesDescription { get; set; } = "";
            public long FileSize { get; set; }
        }
    }
}
