using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.IO;
using MedicalImageAnalysis.WpfClient.Models;

namespace MedicalImageAnalysis.WpfClient.Services;

/// <summary>
/// API服务接口
/// </summary>
public interface IApiService
{
    Task<bool> TestConnectionAsync();
    Task<List<DicomInfo>> GetDicomInstancesAsync();
    Task<DicomInfo?> GetDicomInstanceAsync(Guid id);
    Task<byte[]> GetDicomImageAsync(Guid id);
    Task<Guid> UploadDicomFileAsync(string filePath);
    Task<List<object>> RunYoloInferenceAsync(byte[] imageData, string modelPath, double confidence);
    Task<TrainingProgress> StartTrainingAsync(TrainingConfig config);
    Task<InferenceResult> RunInferenceAsync(string modelPath, string imagePath);
    Task<SystemStatus> GetSystemStatusAsync();
}

/// <summary>
/// API服务实现
/// </summary>
public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ApiService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public ApiService(HttpClient httpClient, ILogger<ApiService> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;

        // 确保HttpClient有正确的BaseAddress
        if (_httpClient.BaseAddress == null)
        {
            var baseUrl = configuration["ApiSettings:BaseUrl"] ?? "http://localhost:5000";
            _httpClient.BaseAddress = new Uri(baseUrl);
            _logger.LogInformation("设置HttpClient BaseAddress: {BaseUrl}", baseUrl);
        }

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
    }

    /// <summary>
    /// 测试连接
    /// </summary>
    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("HttpClient BaseAddress: {BaseAddress}", _httpClient.BaseAddress);
            var response = await _httpClient.GetAsync("/health");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试API连接失败");
            return false;
        }
    }

    /// <summary>
    /// 获取DICOM实例列表
    /// </summary>
    public async Task<List<DicomInfo>> GetDicomInstancesAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("/api/dicom/instances");
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var instances = JsonSerializer.Deserialize<List<DicomInfo>>(json, _jsonOptions);
            
            return instances ?? new List<DicomInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取DICOM实例列表失败");
            throw;
        }
    }

    /// <summary>
    /// 获取DICOM实例详情
    /// </summary>
    public async Task<DicomInfo?> GetDicomInstanceAsync(Guid id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/dicom/instances/{id}");
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return null;
            }
            
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<DicomInfo>(json, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取DICOM实例详情失败: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 获取DICOM图像
    /// </summary>
    public async Task<byte[]> GetDicomImageAsync(Guid id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/dicom/instances/{id}/image");
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadAsByteArrayAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取DICOM图像失败: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 上传DICOM文件
    /// </summary>
    public async Task<Guid> UploadDicomFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"文件不存在: {filePath}");
            }

            using var form = new MultipartFormDataContent();
            var fileContent = new ByteArrayContent(await File.ReadAllBytesAsync(filePath));
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/dicom");
            form.Add(fileContent, "file", Path.GetFileName(filePath));

            var response = await _httpClient.PostAsync("/api/dicom/upload", form);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<Dictionary<string, object>>(json, _jsonOptions);
            
            if (result != null && result.TryGetValue("id", out var idValue))
            {
                return Guid.Parse(idValue.ToString()!);
            }

            throw new InvalidOperationException("上传响应中未找到ID");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上传DICOM文件失败: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 开始训练
    /// </summary>
    public async Task<TrainingProgress> StartTrainingAsync(TrainingConfig config)
    {
        try
        {
            var json = JsonSerializer.Serialize(config, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/yolo/train", content);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync();
            var progress = JsonSerializer.Deserialize<TrainingProgress>(responseJson, _jsonOptions);
            
            return progress ?? new TrainingProgress();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始训练失败");
            throw;
        }
    }

    /// <summary>
    /// 运行推理
    /// </summary>
    public async Task<InferenceResult> RunInferenceAsync(string modelPath, string imagePath)
    {
        try
        {
            using var form = new MultipartFormDataContent();
            
            // 添加模型文件
            if (File.Exists(modelPath))
            {
                var modelContent = new ByteArrayContent(await File.ReadAllBytesAsync(modelPath));
                form.Add(modelContent, "model", Path.GetFileName(modelPath));
            }
            
            // 添加图像文件
            if (File.Exists(imagePath))
            {
                var imageContent = new ByteArrayContent(await File.ReadAllBytesAsync(imagePath));
                form.Add(imageContent, "image", Path.GetFileName(imagePath));
            }

            var response = await _httpClient.PostAsync("/api/yolo/inference", form);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<InferenceResult>(json, _jsonOptions);
            
            return result ?? new InferenceResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "运行推理失败");
            throw;
        }
    }

    /// <summary>
    /// 获取系统状态
    /// </summary>
    public async Task<SystemStatus> GetSystemStatusAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("/api/system/status");
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var status = JsonSerializer.Deserialize<SystemStatus>(json, _jsonOptions);
            
            return status ?? new SystemStatus();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统状态失败");
            throw;
        }
    }

    /// <summary>
    /// 运行YOLO推理
    /// </summary>
    public async Task<List<object>> RunYoloInferenceAsync(byte[] imageData, string modelPath, double confidence)
    {
        try
        {
            using var form = new MultipartFormDataContent();

            // 添加图像数据
            var imageContent = new ByteArrayContent(imageData);
            form.Add(imageContent, "image", "image.jpg");

            // 添加模型路径
            form.Add(new StringContent(modelPath), "modelPath");

            // 添加置信度
            var config = new { confidence_threshold = confidence };
            var configJson = JsonSerializer.Serialize(config, _jsonOptions);
            form.Add(new StringContent(configJson), "config");

            var response = await _httpClient.PostAsync("/api/yolo/infer", form);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync();
            var detections = JsonSerializer.Deserialize<List<object>>(responseJson, _jsonOptions);

            return detections ?? new List<object>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YOLO推理失败");
            throw;
        }
    }
}
