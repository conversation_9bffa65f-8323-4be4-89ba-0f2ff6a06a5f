<UserControl x:Class="MedicalImageAnalysis.WpfClient.Views.DataManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}" 
                BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal" Margin="16,8">
                <Button Style="{StaticResource MaterialDesignRaisedButton}" 
                        Command="{Binding CreateProjectCommand}" Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Margin="0,0,8,0"/>
                        <TextBlock Text="新建项目"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}" 
                        Command="{Binding OpenProjectCommand}" Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FolderOpen" Margin="0,0,8,0"/>
                        <TextBlock Text="打开项目"/>
                    </StackPanel>
                </Button>
                
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="8,0"/>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}" 
                        Command="{Binding ImportFilesCommand}" Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Import" Margin="0,0,8,0"/>
                        <TextBlock Text="导入文件"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}" 
                        Command="{Binding ImportFolderCommand}" Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FolderUpload" Margin="0,0,8,0"/>
                        <TextBlock Text="导入文件夹"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}" 
                        Command="{Binding ExportFilesCommand}" Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Export" Margin="0,0,8,0"/>
                        <TextBlock Text="导出文件"/>
                    </StackPanel>
                </Button>
                
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="8,0"/>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}" 
                        Command="{Binding RefreshFilesCommand}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" Margin="0,0,8,0"/>
                        <TextBlock Text="刷新"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧项目面板 -->
            <Border Grid.Column="0" Background="{DynamicResource MaterialDesignCardBackground}" 
                    BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 项目标题 -->
                    <TextBlock Grid.Row="0" Text="项目列表" Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                               Margin="16,16,16,8"/>
                    
                    <!-- 项目列表 -->
                    <ListBox Grid.Row="1" ItemsSource="{Binding Projects}" 
                             SelectedItem="{Binding SelectedProject}" Margin="8">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Padding="8" Margin="4" Background="{DynamicResource MaterialDesignCardBackground}"
                                        materialDesign:ShadowAssist.ShadowDepth="Depth1">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        
                                        <TextBlock Grid.Row="0" Text="{Binding Name}" FontWeight="Bold"/>
                                        <TextBlock Grid.Row="1" Text="{Binding Description}" 
                                                   TextWrapping="Wrap" Opacity="0.7" FontSize="12"/>
                                        <TextBlock Grid.Row="2" Text="{Binding ModifiedTime, StringFormat='修改时间: {0:yyyy-MM-dd}'}" 
                                                   FontSize="10" Opacity="0.5"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                    
                    <!-- 项目操作按钮 -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="8">
                        <Button Style="{StaticResource MaterialDesignIconButton}" 
                                Command="{Binding DeleteProjectCommand}" ToolTip="删除项目">
                            <materialDesign:PackIcon Kind="Delete"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" 
                          Background="{DynamicResource MaterialDesignDivider}"/>
            
            <!-- 右侧文件面板 -->
            <Grid Grid.Column="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 搜索和过滤栏 -->
                <Border Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}" 
                        BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,0,1">
                    <Grid Margin="16,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 搜索框 -->
                        <TextBox Grid.Column="0" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" 
                                 materialDesign:HintAssist.Hint="搜索文件..." 
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,8,0"/>
                        
                        <!-- 文件类型过滤 -->
                        <ComboBox Grid.Column="1" ItemsSource="{Binding FileTypes}" 
                                  SelectedItem="{Binding SelectedFileType}" 
                                  materialDesign:HintAssist.Hint="文件类型" 
                                  Style="{StaticResource MaterialDesignOutlinedComboBox}" 
                                  Width="120" Margin="0,0,8,0"/>
                        
                        <!-- 排序方式 -->
                        <ComboBox Grid.Column="2" ItemsSource="{Binding SortOptions}" 
                                  SelectedItem="{Binding SelectedSortBy}" 
                                  materialDesign:HintAssist.Hint="排序方式" 
                                  Style="{StaticResource MaterialDesignOutlinedComboBox}" 
                                  Width="120" Margin="0,0,8,0"/>
                        
                        <!-- 清除搜索 -->
                        <Button Grid.Column="3" Style="{StaticResource MaterialDesignIconButton}" 
                                Command="{Binding ClearSearchCommand}" ToolTip="清除搜索">
                            <materialDesign:PackIcon Kind="Close"/>
                        </Button>
                    </Grid>
                </Border>
                
                <!-- 统计信息栏 -->
                <Border Grid.Row="1" Background="{DynamicResource MaterialDesignCardBackground}" Padding="16,8">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="{Binding TotalFileCount, StringFormat='文件数量: {0}'}" Margin="0,0,16,0"/>
                        <TextBlock Text="{Binding TotalFileSize, StringFormat='总大小: {0:N0} 字节'}" Margin="0,0,16,0"/>
                        <TextBlock Text="{Binding StatusMessage}" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </Border>
                
                <!-- 文件列表 -->
                <DataGrid Grid.Row="2" ItemsSource="{Binding Files}" 
                          SelectedItem="{Binding SelectedFile}" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False" 
                          CanUserDeleteRows="False" 
                          IsReadOnly="True" 
                          GridLinesVisibility="Horizontal" 
                          HeadersVisibility="Column"
                          materialDesign:DataGridAssist.CellPadding="8,4"
                          materialDesign:DataGridAssist.ColumnHeaderPadding="8,4">
                    
                    <DataGrid.Columns>
                        <DataGridTemplateColumn Header="类型" Width="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <materialDesign:PackIcon Kind="{Binding Type, Converter={StaticResource FileTypeToIconConverter}}" 
                                                             HorizontalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <DataGridTextColumn Header="文件名" Binding="{Binding Name}" Width="*"/>
                        <DataGridTextColumn Header="大小" Binding="{Binding Size, Converter={StaticResource FileSizeConverter}}" Width="100"/>
                        <DataGridTextColumn Header="类型" Binding="{Binding Type}" Width="100"/>
                        <DataGridTextColumn Header="修改时间" Binding="{Binding ModifiedTime, StringFormat='yyyy-MM-dd HH:mm'}" Width="140"/>
                        
                        <DataGridTemplateColumn Header="操作" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignIconButton}" 
                                                Command="{Binding DataContext.ViewFileDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                                                CommandParameter="{Binding}" ToolTip="查看详情" Margin="2">
                                            <materialDesign:PackIcon Kind="Information" Width="16" Height="16"/>
                                        </Button>
                                        <Button Style="{StaticResource MaterialDesignIconButton}" 
                                                Command="{Binding DataContext.DeleteFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                                                CommandParameter="{Binding}" ToolTip="删除文件" Margin="2">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                    
                    <DataGrid.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="查看详情" Command="{Binding ViewFileDetailsCommand}">
                                <MenuItem.Icon>
                                    <materialDesign:PackIcon Kind="Information"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="导出文件" Command="{Binding ExportFilesCommand}">
                                <MenuItem.Icon>
                                    <materialDesign:PackIcon Kind="Export"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <Separator/>
                            <MenuItem Header="删除文件" Command="{Binding DeleteFileCommand}">
                                <MenuItem.Icon>
                                    <materialDesign:PackIcon Kind="Delete"/>
                                </MenuItem.Icon>
                            </MenuItem>
                        </ContextMenu>
                    </DataGrid.ContextMenu>
                </DataGrid>
            </Grid>
        </Grid>
        
        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="{DynamicResource MaterialDesignPaper}" 
                BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,1,0,0">
            <Grid Margin="16,4">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <ProgressBar Value="{Binding ProgressValue}" 
                                 Visibility="{Binding IsProgressVisible, Converter={StaticResource BooleanToVisibilityConverter}}" 
                                 Width="100" Height="4" Margin="0,0,8,0"/>
                    <materialDesign:PackIcon Kind="Loading" 
                                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}" 
                                             Width="16" Height="16">
                        <materialDesign:PackIcon.RenderTransform>
                            <RotateTransform/>
                        </materialDesign:PackIcon.RenderTransform>
                        <materialDesign:PackIcon.Triggers>
                            <EventTrigger RoutedEvent="Loaded">
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Angle" 
                                                         From="0" To="360" Duration="0:0:1"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </materialDesign:PackIcon.Triggers>
                    </materialDesign:PackIcon>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
