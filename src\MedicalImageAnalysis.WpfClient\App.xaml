<Application x:Class="MedicalImageAnalysis.WpfClient.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:MedicalImageAnalysis.WpfClient.Converters"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Teal" SecondaryColor="LightBlue" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- 自定义样式 -->
                <ResourceDictionary Source="Styles/CustomStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 医学影像主题颜色 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#009688"/><!-- 青绿色，医学专业感 -->
            <SolidColorBrush x:Key="SecondaryBrush" Color="#03DAC6"/><!-- 浅青色 -->
            <SolidColorBrush x:Key="AccentBrush" Color="#FF6D00"/><!-- 橙色，突出重要信息 -->
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/><!-- 绿色，成功状态 -->
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/><!-- 橙色，警告状态 -->
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/><!-- 红色，错误状态 -->
            <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/><!-- 蓝色，信息状态 -->
            <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/><!-- 浅灰背景 -->
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/><!-- 白色表面 -->
            <SolidColorBrush x:Key="DicomBrush" Color="#263238"/><!-- 深灰，DICOM查看器背景 -->
            
            <!-- 全局字体 -->
            <FontFamily x:Key="PrimaryFont">Microsoft YaHei UI</FontFamily>
            <FontFamily x:Key="MonospaceFont">Consolas</FontFamily>
            
            <!-- 转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
            <local:FileSizeConverter x:Key="FileSizeConverter"/>
            <local:FileTypeToIconConverter x:Key="FileTypeToIconConverter"/>
            <local:ConnectionStatusToIconConverter x:Key="ConnectionStatusToIconConverter"/>
            <local:ConnectionStatusToBrushConverter x:Key="ConnectionStatusToBrushConverter"/>
            <local:PercentageConverter x:Key="PercentageConverter"/>
            <local:ByteSizeConverter x:Key="ByteSizeConverter"/>
            <local:TimeSpanConverter x:Key="TimeSpanConverter"/>
            
            <!-- 数据模板 -->
            <DataTemplate x:Key="LoadingTemplate">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Loading"
                                           Width="24" Height="24"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="加载中..." 
                             VerticalAlignment="Center"
                             FontFamily="{StaticResource PrimaryFont}"/>
                </StackPanel>
            </DataTemplate>
            
            <DataTemplate x:Key="ErrorTemplate">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AlertCircle" 
                                           Width="24" Height="24" 
                                           Foreground="{StaticResource ErrorBrush}"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding}" 
                             VerticalAlignment="Center"
                             Foreground="{StaticResource ErrorBrush}"
                             FontFamily="{StaticResource PrimaryFont}"/>
                </StackPanel>
            </DataTemplate>
        </ResourceDictionary>
    </Application.Resources>
</Application>
