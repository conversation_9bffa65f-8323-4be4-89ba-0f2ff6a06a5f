@echo off
chcp 65001 >nul
echo ========================================
echo 医学影像解析系统 - 桌面端应用启动器
echo ========================================
echo.

echo 正在启动医学影像解析系统桌面端应用...
echo.

REM 检查是否已有应用在运行
tasklist /FI "IMAGENAME eq MedicalImageAnalysis.Wpf.exe" 2>NUL | find /I /N "MedicalImageAnalysis.Wpf.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo 检测到应用已在运行中...
    echo 如果看不到窗口，请检查任务栏或最小化的窗口。
    echo.
    pause
    exit /b
)

REM 构建应用（如果需要）
echo 正在检查应用是否需要构建...
if not exist "src\MedicalImageAnalysis.Wpf\bin\Debug\net8.0-windows\MedicalImageAnalysis.Wpf.exe" (
    echo 应用未构建，正在构建...
    dotnet build src\MedicalImageAnalysis.Wpf\MedicalImageAnalysis.Wpf.csproj --configuration Debug
    if errorlevel 1 (
        echo 构建失败！请检查错误信息。
        pause
        exit /b 1
    )
)

REM 启动应用
echo 启动应用...
start "" "src\MedicalImageAnalysis.Wpf\bin\Debug\net8.0-windows\MedicalImageAnalysis.Wpf.exe"

echo.
echo 应用启动命令已执行。
echo 如果应用没有显示，请检查：
echo 1. 任务栏是否有应用图标
echo 2. 是否被最小化
echo 3. 查看日志文件：src\MedicalImageAnalysis.Wpf\bin\Debug\net8.0-windows\logs\
echo.
echo 按任意键退出...
pause >nul
