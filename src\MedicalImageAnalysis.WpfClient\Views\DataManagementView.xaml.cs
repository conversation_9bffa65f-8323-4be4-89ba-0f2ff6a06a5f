using Microsoft.Extensions.DependencyInjection;
using MedicalImageAnalysis.WpfClient.ViewModels;
using System.Windows.Controls;

namespace MedicalImageAnalysis.WpfClient.Views;

/// <summary>
/// DataManagementView.xaml 的交互逻辑
/// </summary>
public partial class DataManagementView : UserControl
{
    public DataManagementView()
    {
        InitializeComponent();
        
        // 通过依赖注入获取ViewModel
        if (App.Current is App app && app.ServiceProvider != null)
        {
            DataContext = app.ServiceProvider.GetService<DataManagementViewModel>();
        }
    }
}
