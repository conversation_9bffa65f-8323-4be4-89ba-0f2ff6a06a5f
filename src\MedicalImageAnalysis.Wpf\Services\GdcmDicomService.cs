using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;
using Microsoft.Extensions.Logging;
using FellowOakDicom;
using FellowOakDicom.Imaging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Wpf.Services
{
    /// <summary>
    /// 增强的DICOM服务实现
    /// 基于fo-dicom库提供高性能的DICOM文件解析和图像处理功能
    /// 模拟GDCM风格的API和功能
    /// </summary>
    public class GdcmDicomService
    {
        private readonly ILogger<GdcmDicomService> _logger;

        public GdcmDicomService(ILogger<GdcmDicomService> logger)
        {
            _logger = logger;
            InitializeGdcm();
        }

        /// <summary>
        /// 初始化增强DICOM服务
        /// </summary>
        private void InitializeGdcm()
        {
            try
            {
                // 初始化fo-dicom设置
                _logger.LogInformation("正在初始化增强DICOM服务...");

                // 设置fo-dicom编码（只读属性，无需设置）
                // DicomEncoding.Default = System.Text.Encoding.UTF8;

                _logger.LogInformation("增强DICOM服务初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "增强DICOM服务初始化失败");
                throw;
            }
        }

        /// <summary>
        /// 使用fo-dicom解析DICOM文件
        /// </summary>
        public async Task<DicomInstance> ParseDicomFileAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    _logger.LogInformation("开始解析DICOM文件: {FilePath}", filePath);

                    if (!File.Exists(filePath))
                    {
                        throw new FileNotFoundException($"DICOM文件不存在: {filePath}");
                    }

                    // 使用fo-dicom读取文件
                    var dicomFile = DicomFile.Open(filePath);
                    var dataset = dicomFile.Dataset;

                    // 提取DICOM标签信息
                    var dicomInstance = ExtractDicomMetadata(dataset, filePath);

                    _logger.LogInformation("DICOM文件解析完成: {SopInstanceUid}", dicomInstance.SopInstanceUid);
                    return dicomInstance;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析DICOM文件失败: {FilePath}", filePath);
                    throw;
                }
            });
        }

        /// <summary>
        /// 提取DICOM元数据
        /// </summary>
        private DicomInstance ExtractDicomMetadata(DicomDataset dataset, string filePath)
        {
            var instance = new DicomInstance
            {
                Id = Guid.NewGuid()
            };

            try
            {
                // 提取基本DICOM标签
                instance.SopInstanceUid = GetStringValue(dataset, DicomTag.SOPInstanceUID) ?? Guid.NewGuid().ToString();

                // 图像相关信息
                instance.Rows = GetIntValue(dataset, DicomTag.Rows) ?? 0;
                instance.Columns = GetIntValue(dataset, DicomTag.Columns) ?? 0;
                instance.BitsAllocated = GetIntValue(dataset, DicomTag.BitsAllocated) ?? 16;
                instance.BitsStored = GetIntValue(dataset, DicomTag.BitsStored) ?? 16;
                instance.HighBit = GetIntValue(dataset, DicomTag.HighBit) ?? 15;
                instance.PixelRepresentation = GetIntValue(dataset, DicomTag.PixelRepresentation) ?? 0;
                
                // 窗宽窗位
                instance.WindowCenter = GetDoubleValue(dataset, DicomTag.WindowCenter) ?? 40.0;
                instance.WindowWidth = GetDoubleValue(dataset, DicomTag.WindowWidth) ?? 400.0;

                // 像素间距
                var pixelSpacing = GetStringValue(dataset, DicomTag.PixelSpacing);
                if (!string.IsNullOrEmpty(pixelSpacing))
                {
                    var spacing = pixelSpacing.Split('\\');
                    if (spacing.Length >= 2)
                    {
                        if (double.TryParse(spacing[0], out double rowSpacing) &&
                            double.TryParse(spacing[1], out double colSpacing))
                        {
                            instance.PixelSpacing = (colSpacing, rowSpacing);
                        }
                    }
                }

                // 图像位置
                var imagePosition = GetStringValue(dataset, DicomTag.ImagePositionPatient);
                if (!string.IsNullOrEmpty(imagePosition))
                {
                    var positions = imagePosition.Split('\\');
                    if (positions.Length >= 3 &&
                        double.TryParse(positions[0], out double x) &&
                        double.TryParse(positions[1], out double y) &&
                        double.TryParse(positions[2], out double z))
                    {
                        instance.ImagePosition = (x, y, z);
                    }
                }

                // 图像方向
                var imageOrientation = GetStringValue(dataset, DicomTag.ImageOrientationPatient);
                if (!string.IsNullOrEmpty(imageOrientation))
                {
                    var orientations = imageOrientation.Split('\\');
                    if (orientations.Length >= 6)
                    {
                        var orientationArray = new double[6];
                        for (int i = 0; i < 6; i++)
                        {
                            if (double.TryParse(orientations[i], out double value))
                            {
                                orientationArray[i] = value;
                            }
                        }
                        instance.ImageOrientationPatient = orientationArray;
                    }
                }

                // 层厚和层间距
                instance.SliceThickness = GetDoubleValue(dataset, DicomTag.SliceThickness) ?? 1.0;
                instance.SliceLocation = GetDoubleValue(dataset, DicomTag.SliceLocation) ?? 0.0;

                // 实例编号
                instance.InstanceNumber = GetIntValue(dataset, DicomTag.InstanceNumber) ?? 1;

                _logger.LogDebug("提取DICOM元数据完成: {SopInstanceUid}", instance.SopInstanceUid);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取DICOM元数据时发生部分错误，使用默认值");
            }

            return instance;
        }

        /// <summary>
        /// 从数据集中获取字符串值
        /// </summary>
        private string? GetStringValue(DicomDataset dataset, DicomTag tag)
        {
            try
            {
                if (dataset.Contains(tag))
                {
                    return dataset.GetSingleValueOrDefault(tag, string.Empty);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "获取DICOM标签字符串值失败: {Tag}", tag.ToString());
            }
            return null;
        }

        /// <summary>
        /// 从数据集中获取整数值
        /// </summary>
        private int? GetIntValue(DicomDataset dataset, DicomTag tag)
        {
            try
            {
                if (dataset.Contains(tag))
                {
                    return dataset.GetSingleValueOrDefault(tag, 0);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "获取DICOM标签整数值失败: {Tag}", tag.ToString());
            }
            return null;
        }

        /// <summary>
        /// 从数据集中获取双精度浮点值
        /// </summary>
        private double? GetDoubleValue(DicomDataset dataset, DicomTag tag)
        {
            try
            {
                if (dataset.Contains(tag))
                {
                    return dataset.GetSingleValueOrDefault(tag, 0.0);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "获取DICOM标签双精度值失败: {Tag}", tag.ToString());
            }
            return null;
        }

        /// <summary>
        /// 使用fo-dicom提取像素数据并转换为WPF图像
        /// </summary>
        public async Task<BitmapSource?> ExtractImageAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    _logger.LogInformation("开始提取DICOM图像: {FilePath}", filePath);

                    var dicomFile = DicomFile.Open(filePath);
                    var dataset = dicomFile.Dataset;

                    // 获取图像信息
                    var rows = GetIntValue(dataset, DicomTag.Rows) ?? 0;
                    var columns = GetIntValue(dataset, DicomTag.Columns) ?? 0;
                    var bitsAllocated = GetIntValue(dataset, DicomTag.BitsAllocated) ?? 16;

                    if (rows == 0 || columns == 0)
                    {
                        throw new InvalidOperationException("无效的图像尺寸");
                    }

                    // 直接从数据集提取像素数据
                    var bitmapSource = ExtractPixelDataToBitmapSource(dataset, columns, rows, bitsAllocated);

                    _logger.LogInformation("DICOM图像提取完成: {Width}x{Height}", columns, rows);
                    return bitmapSource;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "提取DICOM图像失败: {FilePath}", filePath);
                    return null;
                }
            });
        }

        /// <summary>
        /// 直接从DICOM数据集提取像素数据并转换为BitmapSource
        /// </summary>
        private BitmapSource ExtractPixelDataToBitmapSource(DicomDataset dataset, int width, int height, int bitsAllocated)
        {
            try
            {
                _logger.LogInformation("开始提取像素数据: {Width}x{Height}, {BitsAllocated}位", width, height, bitsAllocated);

                // 获取像素数据
                var pixelData = DicomPixelData.Create(dataset);
                var frame = pixelData.GetFrame(0);
                var rawData = frame.Data;

                _logger.LogInformation("原始像素数据长度: {Length} 字节", rawData.Length);

                // 根据位深度处理像素数据
                byte[] displayData;
                System.Windows.Media.PixelFormat pixelFormat;
                int stride;

                if (bitsAllocated == 8)
                {
                    // 8位数据直接使用
                    displayData = rawData;
                    pixelFormat = System.Windows.Media.PixelFormats.Gray8;
                    stride = width;
                }
                else if (bitsAllocated == 16)
                {
                    // 16位数据转换为8位显示
                    displayData = Convert16BitTo8Bit(rawData);
                    pixelFormat = System.Windows.Media.PixelFormats.Gray8;
                    stride = width;
                }
                else
                {
                    _logger.LogWarning("不支持的位深度: {BitsAllocated}, 使用默认图像", bitsAllocated);
                    return CreateDefaultImage(width, height);
                }

                // 验证数据长度
                var expectedLength = width * height * (bitsAllocated == 8 ? 1 : 1); // 显示数据总是8位
                if (displayData.Length < expectedLength)
                {
                    _logger.LogWarning("像素数据长度不足: 期望 {Expected}, 实际 {Actual}", expectedLength, displayData.Length);
                    return CreateDefaultImage(width, height);
                }

                // 创建BitmapSource
                var bitmapSource = BitmapSource.Create(
                    width, height,
                    96, 96, // DPI
                    pixelFormat,
                    null, // palette
                    displayData,
                    stride);

                bitmapSource.Freeze(); // 冻结以提高性能

                _logger.LogInformation("成功创建BitmapSource: {Width}x{Height}", width, height);
                return bitmapSource;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取像素数据失败");
                return CreateDefaultImage(width, height);
            }
        }

        /// <summary>
        /// 将16位像素数据转换为8位用于显示
        /// </summary>
        private byte[] Convert16BitTo8Bit(byte[] data16Bit)
        {
            var data8Bit = new byte[data16Bit.Length / 2];

            for (int i = 0; i < data8Bit.Length; i++)
            {
                // 读取16位值（小端序）
                var value16 = BitConverter.ToUInt16(data16Bit, i * 2);

                // 转换为8位（取高8位）
                data8Bit[i] = (byte)(value16 >> 8);
            }

            return data8Bit;
        }

        /// <summary>
        /// 创建默认测试图像
        /// </summary>
        private BitmapSource CreateDefaultImage(int width, int height)
        {
            var defaultData = new byte[width * height];

            // 创建一个简单的测试图案
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    var index = y * width + x;

                    // 创建棋盘图案
                    var checkSize = 32;
                    var checkX = x / checkSize;
                    var checkY = y / checkSize;
                    var isWhite = (checkX + checkY) % 2 == 0;

                    defaultData[index] = isWhite ? (byte)200 : (byte)50;
                }
            }

            return BitmapSource.Create(
                width, height,
                96, 96,
                System.Windows.Media.PixelFormats.Gray8,
                null,
                defaultData,
                width);
        }

        /// <summary>
        /// 验证文件是否为有效的DICOM文件
        /// </summary>
        public async Task<bool> IsValidDicomFileAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    if (!File.Exists(filePath))
                        return false;

                    DicomFile.Open(filePath);
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "验证DICOM文件失败: {FilePath}", filePath);
                    return false;
                }
            });
        }

        /// <summary>
        /// 获取DICOM文件的基本信息
        /// </summary>
        public async Task<Dictionary<string, object>> GetDicomInfoAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                var info = new Dictionary<string, object>();

                try
                {
                    var dicomFile = DicomFile.Open(filePath);
                    var dataset = dicomFile.Dataset;

                    // 添加常用的DICOM标签信息
                    var commonTags = new Dictionary<string, DicomTag>
                    {
                        ["Patient Name"] = DicomTag.PatientName,
                        ["Patient ID"] = DicomTag.PatientID,
                        ["Study Date"] = DicomTag.StudyDate,
                        ["Study Time"] = DicomTag.StudyTime,
                        ["Modality"] = DicomTag.Modality,
                        ["Manufacturer"] = DicomTag.Manufacturer,
                        ["Institution Name"] = DicomTag.InstitutionName,
                        ["Study Description"] = DicomTag.StudyDescription,
                        ["Series Description"] = DicomTag.SeriesDescription,
                        ["Rows"] = DicomTag.Rows,
                        ["Columns"] = DicomTag.Columns,
                        ["Bits Allocated"] = DicomTag.BitsAllocated,
                        ["Pixel Spacing"] = DicomTag.PixelSpacing,
                        ["Slice Thickness"] = DicomTag.SliceThickness
                    };

                    foreach (var kvp in commonTags)
                    {
                        var value = GetStringValue(dataset, kvp.Value);
                        if (!string.IsNullOrEmpty(value))
                        {
                            info[kvp.Key] = value;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取DICOM信息失败: {FilePath}", filePath);
                }

                return info;
            });
        }

        public void Dispose()
        {
            // 增强DICOM服务资源清理
            _logger.LogInformation("增强DICOM服务已释放");
        }
    }
}
