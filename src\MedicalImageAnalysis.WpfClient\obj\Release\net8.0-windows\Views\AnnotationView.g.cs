﻿#pragma checksum "..\..\..\..\Views\AnnotationView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9FCEE5CEF717B57D3DFC50F2DF423B35AC8B998D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using ScottPlot;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.WpfClient.Views {
    
    
    /// <summary>
    /// AnnotationView
    /// </summary>
    public partial class AnnotationView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 210 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas AnnotationCanvas;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Views\AnnotationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas PredictionCanvas;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.WpfClient;V1.0.0.0;component/views/annotationview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AnnotationView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AnnotationCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 212 "..\..\..\..\Views\AnnotationView.xaml"
            this.AnnotationCanvas.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.AnnotationCanvas_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 213 "..\..\..\..\Views\AnnotationView.xaml"
            this.AnnotationCanvas.MouseMove += new System.Windows.Input.MouseEventHandler(this.AnnotationCanvas_MouseMove);
            
            #line default
            #line hidden
            
            #line 214 "..\..\..\..\Views\AnnotationView.xaml"
            this.AnnotationCanvas.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.AnnotationCanvas_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PredictionCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

