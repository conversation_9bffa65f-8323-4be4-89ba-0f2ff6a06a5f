<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MedicalImageAnalysis.Api</name>
    </assembly>
    <members>
        <member name="T:MedicalImageAnalysis.Api.Controllers.AnnotationController">
            <summary>
            标注管理控制器，提供智能标注和标注管理 API
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.AnnotationController.GenerateAnnotations(Microsoft.AspNetCore.Http.IFormFile,MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig)">
            <summary>
            自动生成标注
            </summary>
            <param name="dicomFile">DICOM 文件</param>
            <param name="config">标注配置</param>
            <returns>生成的标注</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.AnnotationController.ValidateAnnotations(MedicalImageAnalysis.Api.Models.ValidateAnnotationsRequest)">
            <summary>
            验证标注质量
            </summary>
            <param name="request">验证请求</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.AnnotationController.ConvertAnnotationFormat(MedicalImageAnalysis.Api.Models.ConvertAnnotationFormatRequest)">
            <summary>
            转换标注格式
            </summary>
            <param name="request">转换请求</param>
            <returns>转换后的标注数据</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.AnnotationController.GenerateStatistics(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation})">
            <summary>
            生成标注统计信息
            </summary>
            <param name="annotations">标注数据</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.AnnotationController.DetectAnomalies(MedicalImageAnalysis.Api.Models.DetectAnomaliesRequest)">
            <summary>
            检测标注异常
            </summary>
            <param name="request">检测请求</param>
            <returns>异常检测结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.AnnotationController.MergeOverlappingAnnotations(MedicalImageAnalysis.Api.Models.MergeOverlappingAnnotationsRequest)">
            <summary>
            合并重叠标注
            </summary>
            <param name="request">合并请求</param>
            <returns>合并后的标注</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.AnnotationController.GetSupportedFormats">
            <summary>
            获取支持的标注格式
            </summary>
            <returns>支持的格式列表</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.AnnotationController.GetDefaultConfig">
            <summary>
            获取默认标注配置
            </summary>
            <returns>默认配置</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.AnnotationController.GetDefaultValidationRules">
            <summary>
            获取默认验证规则
            </summary>
            <returns>默认验证规则</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Api.Controllers.DirectoryController">
            <summary>
            目录管理控制器，提供文件夹操作和系统目录访问 API
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSystemDirectories">
            <summary>
            获取系统目录信息
            </summary>
            <returns>系统目录信息</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.OpenDirectory(System.String)">
            <summary>
            打开指定目录
            </summary>
            <param name="directoryPath">目录路径</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.OpenSystemDirectory(System.String)">
            <summary>
            打开系统预定义目录
            </summary>
            <param name="directoryType">目录类型</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.GetDirectoryContent(System.String,System.Boolean)">
            <summary>
            获取目录内容
            </summary>
            <param name="directoryPath">目录路径</param>
            <param name="includeSubdirectories">是否包含子目录</param>
            <returns>目录内容</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.CreateDirectory(System.String)">
            <summary>
            创建目录
            </summary>
            <param name="directoryPath">目录路径</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.DeleteDirectory(System.String,System.Boolean)">
            <summary>
            删除目录
            </summary>
            <param name="directoryPath">目录路径</param>
            <param name="recursive">是否递归删除</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.GetDirectorySize(System.String)">
            <summary>
            获取目录大小
            </summary>
            <param name="directoryPath">目录路径</param>
            <returns>目录大小（字节）</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.CleanupTempFiles(System.Int32)">
            <summary>
            清理临时文件
            </summary>
            <param name="olderThanDays">清理多少天前的文件</param>
            <returns>清理的文件数量</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.GetDiskUsage">
            <summary>
            获取磁盘使用情况
            </summary>
            <returns>磁盘使用情况</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.DirectoryController.GetSupportedDirectoryTypes">
            <summary>
            获取支持的目录类型
            </summary>
            <returns>支持的目录类型列表</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Api.Controllers.StudyController">
            <summary>
            研究处理控制器，提供 DICOM 研究的处理和管理 API
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.StudyController.UploadAndProcessStudy(Microsoft.AspNetCore.Http.IFormFileCollection,MedicalImageAnalysis.Application.Services.StudyProcessingConfig)">
            <summary>
            上传并处理 DICOM 文件
            </summary>
            <param name="files">DICOM 文件集合</param>
            <param name="config">处理配置</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.StudyController.ValidateDicomFile(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            验证 DICOM 文件
            </summary>
            <param name="file">DICOM 文件</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.StudyController.ExtractMetadata(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            提取 DICOM 文件元数据
            </summary>
            <param name="file">DICOM 文件</param>
            <returns>元数据</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.StudyController.GetStudyStatistics(System.Guid)">
            <summary>
            获取研究统计信息
            </summary>
            <param name="studyId">研究ID</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.StudyController.GetHealth">
            <summary>
            获取系统健康状态
            </summary>
            <returns>健康状态</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.StudyController.GetSupportedFormats">
            <summary>
            获取支持的图像格式
            </summary>
            <returns>支持的格式列表</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.StudyController.GetDefaultConfig">
            <summary>
            获取默认处理配置
            </summary>
            <returns>默认配置</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Api.Controllers.YoloController">
            <summary>
            YOLO 模型管理控制器，提供模型训练、验证和推理 API
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.TrainModel(MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig)">
            <summary>
            训练 YOLO 模型
            </summary>
            <param name="config">训练配置</param>
            <returns>训练结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.ValidateModel(System.String,System.String)">
            <summary>
            验证模型性能
            </summary>
            <param name="modelPath">模型文件路径</param>
            <param name="validationDataPath">验证数据路径</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.Infer(System.String,Microsoft.AspNetCore.Http.IFormFile,MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig)">
            <summary>
            使用模型进行推理
            </summary>
            <param name="modelPath">模型文件路径</param>
            <param name="image">图像文件</param>
            <param name="config">推理配置</param>
            <returns>检测结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.BatchInfer(System.String,System.String,MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig)">
            <summary>
            批量推理
            </summary>
            <param name="modelPath">模型文件路径</param>
            <param name="imageDirectory">图像目录路径</param>
            <param name="config">推理配置</param>
            <returns>批量检测结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.ExportModel(System.String,MedicalImageAnalysis.Core.Interfaces.ModelExportFormat,System.String)">
            <summary>
            导出模型
            </summary>
            <param name="modelPath">模型文件路径</param>
            <param name="exportFormat">导出格式</param>
            <param name="outputPath">输出路径</param>
            <returns>导出的模型路径</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.GetModelInfo(System.String)">
            <summary>
            获取模型信息
            </summary>
            <param name="modelPath">模型文件路径</param>
            <returns>模型信息</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.ValidateDataset(System.String)">
            <summary>
            验证数据集
            </summary>
            <param name="datasetPath">数据集路径</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.AugmentData(System.String,System.String,MedicalImageAnalysis.Core.Interfaces.DataAugmentationConfig)">
            <summary>
            生成数据增强
            </summary>
            <param name="sourceDataPath">源数据路径</param>
            <param name="outputPath">输出路径</param>
            <param name="config">增强配置</param>
            <returns>增强后的数据路径</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.GetDefaultTrainingConfig">
            <summary>
            获取默认训练配置
            </summary>
            <returns>默认训练配置</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Api.Controllers.YoloController.GetDefaultInferenceConfig">
            <summary>
            获取默认推理配置
            </summary>
            <returns>默认推理配置</returns>
        </member>
        <member name="T:MedicalImageAnalysis.Api.Models.ValidateAnnotationsRequest">
            <summary>
            验证标注请求模型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.ValidateAnnotationsRequest.Annotations">
            <summary>
            标注数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.ValidateAnnotationsRequest.ValidationRules">
            <summary>
            验证规则
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Api.Models.ConvertAnnotationFormatRequest">
            <summary>
            转换标注格式请求模型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.ConvertAnnotationFormatRequest.Annotations">
            <summary>
            标注数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.ConvertAnnotationFormatRequest.TargetFormat">
            <summary>
            目标格式
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.ConvertAnnotationFormatRequest.ImageWidth">
            <summary>
            图像宽度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.ConvertAnnotationFormatRequest.ImageHeight">
            <summary>
            图像高度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.ConvertAnnotationFormatRequest.ClassMapping">
            <summary>
            类别映射
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Api.Models.DetectAnomaliesRequest">
            <summary>
            检测异常请求模型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.DetectAnomaliesRequest.Annotations">
            <summary>
            标注数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.DetectAnomaliesRequest.DetectionConfig">
            <summary>
            异常检测配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Api.Models.MergeOverlappingAnnotationsRequest">
            <summary>
            合并重叠标注请求模型
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.MergeOverlappingAnnotationsRequest.Annotations">
            <summary>
            标注数据
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Api.Models.MergeOverlappingAnnotationsRequest.MergeConfig">
            <summary>
            合并配置
            </summary>
        </member>
        <member name="T:YoloServicePlaceholder">
            <summary>
            YOLO 服务的占位符实现，用于演示
            </summary>
        </member>
    </members>
</doc>
