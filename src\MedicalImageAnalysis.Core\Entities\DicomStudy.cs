using System.ComponentModel.DataAnnotations;

namespace MedicalImageAnalysis.Core.Entities;

/// <summary>
/// DICOM 研究实体，表示一次完整的医学影像检查
/// </summary>
public class DicomStudy
{
    /// <summary>
    /// 研究唯一标识符
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// DICOM 研究实例 UID
    /// </summary>
    [Required]
    [StringLength(64)]
    public string StudyInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// 患者ID（外键）
    /// </summary>
    public Guid PatientId { get; set; }

    /// <summary>
    /// 患者信息
    /// </summary>
    public Patient Patient { get; set; } = null!;

    /// <summary>
    /// 研究日期时间
    /// </summary>
    public DateTime StudyDateTime { get; set; }

    /// <summary>
    /// 研究日期
    /// </summary>
    public DateTime StudyDate { get; set; }

    /// <summary>
    /// 研究时间
    /// </summary>
    public TimeSpan StudyTime { get; set; }

    /// <summary>
    /// 检查号
    /// </summary>
    [StringLength(64)]
    public string AccessionNumber { get; set; } = string.Empty;

    /// <summary>
    /// 研究ID
    /// </summary>
    [StringLength(16)]
    public string StudyId { get; set; } = string.Empty;

    /// <summary>
    /// 转诊医生姓名
    /// </summary>
    [StringLength(128)]
    public string ReferringPhysicianName { get; set; } = string.Empty;

    /// <summary>
    /// 研究描述
    /// </summary>
    [StringLength(256)]
    public string StudyDescription { get; set; } = string.Empty;

    /// <summary>
    /// 检查部位
    /// </summary>
    [StringLength(64)]
    public string BodyPart { get; set; } = string.Empty;

    /// <summary>
    /// 影像方向
    /// </summary>
    public ImageOrientation Orientation { get; set; }

    /// <summary>
    /// 研究状态
    /// </summary>
    public StudyStatus Status { get; set; } = StudyStatus.Pending;

    /// <summary>
    /// 包含的序列集合
    /// </summary>
    public List<DicomSeries> Series { get; set; } = new();

    /// <summary>
    /// 处理结果集合
    /// </summary>
    public List<ProcessingResult> ProcessingResults { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 机构名称
    /// </summary>
    [StringLength(64)]
    public string InstitutionName { get; set; } = string.Empty;

    /// <summary>
    /// 机构地址
    /// </summary>
    [StringLength(1024)]
    public string InstitutionAddress { get; set; } = string.Empty;

    /// <summary>
    /// 工作站名称
    /// </summary>
    [StringLength(16)]
    public string StationName { get; set; } = string.Empty;

    /// <summary>
    /// 序列描述
    /// </summary>
    [StringLength(64)]
    public string SeriesDescription { get; set; } = string.Empty;

    /// <summary>
    /// 机构部门名称
    /// </summary>
    [StringLength(64)]
    public string InstitutionalDepartmentName { get; set; } = string.Empty;

    /// <summary>
    /// 记录医生
    /// </summary>
    [StringLength(64)]
    public string PhysiciansOfRecord { get; set; } = string.Empty;

    /// <summary>
    /// 执行医生姓名
    /// </summary>
    [StringLength(64)]
    public string PerformingPhysicianName { get; set; } = string.Empty;

    /// <summary>
    /// 阅片医生姓名
    /// </summary>
    [StringLength(64)]
    public string NameOfPhysiciansReadingStudy { get; set; } = string.Empty;

    /// <summary>
    /// 操作员姓名
    /// </summary>
    [StringLength(64)]
    public string OperatorsName { get; set; } = string.Empty;

    /// <summary>
    /// 入院诊断描述
    /// </summary>
    [StringLength(64)]
    public string AdmittingDiagnosesDescription { get; set; } = string.Empty;

    /// <summary>
    /// 制造商型号名称
    /// </summary>
    [StringLength(64)]
    public string ManufacturerModelName { get; set; } = string.Empty;

    /// <summary>
    /// 引用患者序列
    /// </summary>
    [StringLength(64)]
    public string ReferencedPatientSequence { get; set; } = string.Empty;

    /// <summary>
    /// 派生描述
    /// </summary>
    [StringLength(1024)]
    public string DerivationDescription { get; set; } = string.Empty;

    /// <summary>
    /// 扫描选项
    /// </summary>
    [StringLength(64)]
    public string ScanOptions { get; set; } = string.Empty;

    /// <summary>
    /// 对比剂
    /// </summary>
    [StringLength(64)]
    public string ContrastBolusAgent { get; set; } = string.Empty;

    /// <summary>
    /// 获取研究的总切片数
    /// </summary>
    public int TotalSlices => Series.Sum(s => s.Instances.Count);

    /// <summary>
    /// 获取研究的像素间距
    /// </summary>
    public (double X, double Y) PixelSpacing
    {
        get
        {
            var firstInstance = Series.FirstOrDefault()?.Instances.FirstOrDefault();
            return firstInstance?.PixelSpacing ?? (1.0, 1.0);
        }
    }

    /// <summary>
    /// 获取切片厚度
    /// </summary>
    public double SliceThickness
    {
        get
        {
            var firstInstance = Series.FirstOrDefault()?.Instances.FirstOrDefault();
            return firstInstance?.SliceThickness ?? 1.0;
        }
    }
}

/// <summary>
/// 患者信息
/// </summary>
public class PatientInfo
{
    /// <summary>
    /// 患者ID
    /// </summary>
    [StringLength(64)]
    public string PatientId { get; set; } = string.Empty;

    /// <summary>
    /// 患者姓名
    /// </summary>
    [StringLength(128)]
    public string PatientName { get; set; } = string.Empty;

    /// <summary>
    /// 患者年龄
    /// </summary>
    [StringLength(4)]
    public string PatientAge { get; set; } = string.Empty;

    /// <summary>
    /// 患者性别
    /// </summary>
    public Gender PatientSex { get; set; } = Gender.Unknown;

    /// <summary>
    /// 患者出生日期
    /// </summary>
    public DateTime? PatientBirthDate { get; set; }

    /// <summary>
    /// 关联的研究集合
    /// </summary>
    public List<DicomStudy> Studies { get; set; } = new();
}

/// <summary>
/// 性别枚举
/// </summary>
public enum Gender
{
    Unknown = 0,
    Male = 1,
    Female = 2,
    Other = 3
}

/// <summary>
/// 影像方向枚举
/// </summary>
public enum ImageOrientation
{
    Unknown = 0,
    Axial = 1,      // 轴位
    Sagittal = 2,   // 矢状位
    Coronal = 3,    // 冠状位
    Oblique = 4     // 斜位
}

/// <summary>
/// 研究状态枚举
/// </summary>
public enum StudyStatus
{
    Pending = 0,        // 待处理
    Processing = 1,     // 处理中
    Completed = 2,      // 已完成
    Failed = 3,         // 处理失败
    Cancelled = 4       // 已取消
}
