<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MedicalImageAnalysis.Application</name>
    </assembly>
    <members>
        <member name="T:MedicalImageAnalysis.Application.Services.StudyProcessingService">
            <summary>
            研究处理服务，协调 DICOM 解析、图像处理和 AI 分析的完整流程
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Application.Services.StudyProcessingService.#ctor(MedicalImageAnalysis.Core.Interfaces.IDicomService,MedicalImageAnalysis.Core.Interfaces.IImageProcessingService,MedicalImageAnalysis.Core.Interfaces.IYoloService,MedicalImageAnalysis.Core.Interfaces.IAnnotationService,Microsoft.Extensions.Logging.ILogger{MedicalImageAnalysis.Application.Services.StudyProcessingService})">
            <summary>
            初始化研究处理服务
            </summary>
            <param name="dicomService">DICOM服务</param>
            <param name="imageProcessingService">图像处理服务</param>
            <param name="yoloService">YOLO服务</param>
            <param name="annotationService">标注服务</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:MedicalImageAnalysis.Application.Services.StudyProcessingService.ProcessStudyAsync(System.Collections.Generic.IEnumerable{System.String},MedicalImageAnalysis.Application.Services.StudyProcessingConfig,System.IProgress{MedicalImageAnalysis.Application.Services.StudyProcessingProgress},System.Threading.CancellationToken)">
            <summary>
            处理完整的研究流程
            </summary>
            <param name="filePaths">DICOM 文件路径集合</param>
            <param name="processingConfig">处理配置</param>
            <param name="progressCallback">进度回调</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:MedicalImageAnalysis.Application.Services.StudyProcessingService.PreprocessImagesAsync(MedicalImageAnalysis.Core.Entities.DicomStudy,MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions,MedicalImageAnalysis.Application.Services.StudyProcessingProgress,System.IProgress{MedicalImageAnalysis.Application.Services.StudyProcessingProgress},System.Threading.CancellationToken)">
            <summary>
            预处理图像
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Application.Services.StudyProcessingService.PerformAIDetectionAsync(MedicalImageAnalysis.Core.Entities.DicomStudy,MedicalImageAnalysis.Application.Services.StudyProcessingConfig,MedicalImageAnalysis.Application.Services.StudyProcessingProgress,System.IProgress{MedicalImageAnalysis.Application.Services.StudyProcessingProgress},System.Threading.CancellationToken)">
            <summary>
            执行 AI 检测
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Application.Services.StudyProcessingService.ValidateAndOptimizeAnnotationsAsync(MedicalImageAnalysis.Core.Entities.DicomStudy,MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules,MedicalImageAnalysis.Application.Services.StudyProcessingProgress,System.IProgress{MedicalImageAnalysis.Application.Services.StudyProcessingProgress},System.Threading.CancellationToken)">
            <summary>
            验证和优化标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Application.Services.StudyProcessingService.GenerateAnnotationStatisticsAsync(MedicalImageAnalysis.Core.Entities.DicomStudy)">
            <summary>
            生成标注统计信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Application.Services.StudyProcessingConfig">
            <summary>
            研究处理配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.EnableImagePreprocessing">
            <summary>
            是否启用图像预处理
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.PreprocessingOptions">
            <summary>
            预处理选项
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.EnableAIDetection">
            <summary>
            是否启用 AI 检测
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.ModelPath">
            <summary>
            模型路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.InferenceConfig">
            <summary>
            推理配置
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.EnableAnnotationValidation">
            <summary>
            是否启用标注验证
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.ValidationRules">
            <summary>
            验证规则
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.EnableDatasetExport">
            <summary>
            是否启用数据集导出
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.ExportPath">
            <summary>
            导出路径
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingConfig.ExportConfig">
            <summary>
            导出配置
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Application.Services.StudyProcessingProgress">
            <summary>
            研究处理进度
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingProgress.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingProgress.CurrentStep">
            <summary>
            当前步骤
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingProgress.StepProgress">
            <summary>
            当前步骤进度 (0-100)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingProgress.OverallProgress">
            <summary>
            总体进度 (0-100)
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingProgress.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Application.Services.StudyProcessingResult">
            <summary>
            研究处理结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingResult.Study">
            <summary>
            处理的研究
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingResult.Statistics">
            <summary>
            研究统计信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingResult.AnnotationStatistics">
            <summary>
            标注统计信息
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingResult.ExportResult">
            <summary>
            导出结果
            </summary>
        </member>
        <member name="P:MedicalImageAnalysis.Application.Services.StudyProcessingResult.ProcessingTimeMs">
            <summary>
            处理耗时 (毫秒)
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Application.Services.ProcessingStep">
            <summary>
            处理步骤枚举
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Application.Services.ProcessingStep.DicomParsing">
            <summary>
            DICOM 解析
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Application.Services.ProcessingStep.ImagePreprocessing">
            <summary>
            图像预处理
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Application.Services.ProcessingStep.AIDetection">
            <summary>
            AI 检测
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Application.Services.ProcessingStep.AnnotationValidation">
            <summary>
            标注验证
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Application.Services.ProcessingStep.StatisticsGeneration">
            <summary>
            统计信息生成
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Application.Services.ProcessingStep.DatasetExport">
            <summary>
            数据集导出
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Application.Services.ProcessingStep.Completed">
            <summary>
            完成
            </summary>
        </member>
        <member name="F:MedicalImageAnalysis.Application.Services.ProcessingStep.Failed">
            <summary>
            失败
            </summary>
        </member>
    </members>
</doc>
