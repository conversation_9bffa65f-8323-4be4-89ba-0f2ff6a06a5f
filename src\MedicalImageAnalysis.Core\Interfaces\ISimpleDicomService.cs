using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Models;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 简化的DICOM服务接口
/// </summary>
public interface ISimpleDicomService
{
    /// <summary>
    /// 解析DICOM文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>解析结果</returns>
    Task<Models.DicomParseResult> ParseDicomFileAsync(string filePath);

    /// <summary>
    /// 验证DICOM文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>验证结果</returns>
    Task<Models.DicomValidationResult> ValidateDicomFileAsync(string filePath);

    /// <summary>
    /// 提取像素数据
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>像素数据</returns>
    Task<Models.PixelData?> ExtractPixelDataAsync(string filePath);

    /// <summary>
    /// 获取DICOM标签值
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="tag">标签</param>
    /// <returns>标签值</returns>
    Task<string?> GetDicomTagValueAsync(string filePath, uint tag);

    /// <summary>
    /// 转换为标准图像格式
    /// </summary>
    /// <param name="dicomPath">DICOM文件路径</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="format">图像格式</param>
    /// <returns>输出文件路径</returns>
    Task<string?> ConvertToImageAsync(string dicomPath, string outputPath, Models.ImageFormat format = Models.ImageFormat.Png);

    /// <summary>
    /// 批量处理DICOM文件
    /// </summary>
    /// <param name="filePaths">文件路径集合</param>
    /// <param name="progress">进度报告</param>
    /// <returns>批量处理结果</returns>
    Task<Models.BatchProcessResult> ProcessDicomBatchAsync(IEnumerable<string> filePaths, IProgress<string>? progress = null);

    /// <summary>
    /// 提取DICOM元数据
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>元数据</returns>
    Task<Models.DicomMetadata?> ExtractMetadataAsync(string filePath);

    /// <summary>
    /// 获取图像的坐标信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>坐标信息</returns>
    Task<Models.ImageCoordinateInfo?> GetImageCoordinateInfoAsync(string filePath);
}


