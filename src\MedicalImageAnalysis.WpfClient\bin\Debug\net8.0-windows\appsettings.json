{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ApiSettings": {"BaseUrl": "http://localhost:5000", "SignalRUrl": "http://localhost:5000/hubs/processing", "Timeout": 30}, "AppSettings": {"Theme": "Light", "Language": "zh-CN", "AutoConnect": true, "EnableNotifications": true, "DefaultOutputDirectory": "Output", "MaxConcurrentTasks": 3, "SaveWindowState": true}, "DicomSettings": {"SupportedExtensions": [".dcm", ".dicom"], "MaxFileSize": 536870912, "TempDirectory": "Temp"}, "ModelSettings": {"SupportedFormats": [".pt", ".onnx", ".engine"], "DefaultModelType": "yolo11n", "ModelDirectory": "Models"}}