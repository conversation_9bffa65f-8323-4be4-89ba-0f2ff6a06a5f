using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using MedicalImageAnalysis.WpfClient.Models;
using MaterialDesignThemes.Wpf;

namespace MedicalImageAnalysis.WpfClient.Converters;

/// <summary>
/// 连接状态到图标转换器
/// </summary>
public class ConnectionStatusToIconConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ConnectionStatus status)
        {
            return status switch
            {
                ConnectionStatus.Connected => "CheckCircle",
                ConnectionStatus.Connecting => "Loading",
                ConnectionStatus.Disconnected => "CloseCircle",
                ConnectionStatus.Error => "AlertCircle",
                _ => "HelpCircle"
            };
        }
        return "HelpCircle";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 连接状态到画刷转换器
/// </summary>
public class ConnectionStatusToBrushConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ConnectionStatus status)
        {
            return status switch
            {
                ConnectionStatus.Connected => new SolidColorBrush(Colors.Green),
                ConnectionStatus.Connecting => new SolidColorBrush(Colors.Orange),
                ConnectionStatus.Disconnected => new SolidColorBrush(Colors.Gray),
                ConnectionStatus.Error => new SolidColorBrush(Colors.Red),
                _ => new SolidColorBrush(Colors.Gray)
            };
        }
        return new SolidColorBrush(Colors.Gray);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 布尔值到可见性转换器（反向）
/// </summary>
public class InverseBooleanToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? Visibility.Collapsed : Visibility.Visible;
        }
        return Visibility.Visible;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Visibility visibility)
        {
            return visibility != Visibility.Visible;
        }
        return false;
    }
}

/// <summary>
/// 空值到可见性转换器
/// </summary>
public class NullToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value == null ? Visibility.Collapsed : Visibility.Visible;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 字节大小格式化转换器
/// </summary>
public class ByteSizeConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is long bytes)
        {
            return FormatBytes(bytes);
        }
        if (value is int intBytes)
        {
            return FormatBytes(intBytes);
        }
        return "0 B";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }

    private static string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = bytes;
        
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        
        return $"{number:n1} {suffixes[counter]}";
    }
}

/// <summary>
/// 时间跨度格式化转换器
/// </summary>
public class TimeSpanConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
            {
                return $"{(int)timeSpan.TotalDays}天 {timeSpan.Hours}小时";
            }
            if (timeSpan.TotalHours >= 1)
            {
                return $"{(int)timeSpan.TotalHours}小时 {timeSpan.Minutes}分钟";
            }
            if (timeSpan.TotalMinutes >= 1)
            {
                return $"{(int)timeSpan.TotalMinutes}分钟 {timeSpan.Seconds}秒";
            }
            return $"{timeSpan.Seconds}秒";
        }
        return "0秒";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 百分比格式化转换器
/// </summary>
public class PercentageConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is double doubleValue)
        {
            return $"{doubleValue:F1}%";
        }
        if (value is float floatValue)
        {
            return $"{floatValue:F1}%";
        }
        return "0.0%";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string stringValue && stringValue.EndsWith("%"))
        {
            var numberPart = stringValue.Substring(0, stringValue.Length - 1);
            if (double.TryParse(numberPart, out var result))
            {
                return result;
            }
        }
        return 0.0;
    }
}

/// <summary>
/// 多值转换器基类
/// </summary>
public abstract class MultiValueConverterBase : IMultiValueConverter
{
    public abstract object Convert(object[] values, Type targetType, object parameter, CultureInfo culture);

    public virtual object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 多个布尔值的AND操作转换器
/// </summary>
public class MultiBooleanAndConverter : MultiValueConverterBase
{
    public override object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        return values.OfType<bool>().All(b => b);
    }
}

/// <summary>
/// 多个布尔值的OR操作转换器
/// </summary>
public class MultiBooleanOrConverter : MultiValueConverterBase
{
    public override object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        return values.OfType<bool>().Any(b => b);
    }
}

/// <summary>
/// 数值范围验证转换器
/// </summary>
public class NumberRangeConverter : IValueConverter
{
    public double MinValue { get; set; } = double.MinValue;
    public double MaxValue { get; set; } = double.MaxValue;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is double doubleValue)
        {
            return doubleValue >= MinValue && doubleValue <= MaxValue;
        }
        return false;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 文件类型到图标转换器
/// </summary>
public class FileTypeToIconConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string fileType)
        {
            return fileType.ToLower() switch
            {
                "dicom" => PackIconKind.FileImage,
                "jpeg" or "jpg" => PackIconKind.FileImage,
                "png" => PackIconKind.FileImage,
                "bmp" => PackIconKind.FileImage,
                "标注文件" => PackIconKind.FileDocument,
                "json" => PackIconKind.CodeJson,
                "xml" => PackIconKind.FileDocument,
                _ => PackIconKind.File
            };
        }
        return PackIconKind.File;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 文件大小转换器（别名）
/// </summary>
public class FileSizeConverter : IValueConverter
{
    private readonly ByteSizeConverter _byteSizeConverter = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return _byteSizeConverter.Convert(value, targetType, parameter, culture);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return _byteSizeConverter.ConvertBack(value, targetType, parameter, culture);
    }
}
