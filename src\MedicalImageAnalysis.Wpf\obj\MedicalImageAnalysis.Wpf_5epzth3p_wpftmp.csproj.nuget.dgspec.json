{"format": 1, "restore": {"D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Wpf\\MedicalImageAnalysis.Wpf.csproj": {}}, "projects": {"D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Application\\MedicalImageAnalysis.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Application\\MedicalImageAnalysis.Application.csproj", "projectName": "MedicalImageAnalysis.Application", "projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Application\\MedicalImageAnalysis.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj": {"projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.1, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.8.1, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj", "projectName": "MedicalImageAnalysis.Core", "projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "fo-dicom": {"target": "Package", "version": "[5.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\MedicalImageAnalysis.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\MedicalImageAnalysis.Infrastructure.csproj", "projectName": "MedicalImageAnalysis.Infrastructure", "projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\MedicalImageAnalysis.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj": {"projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "false", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.ML": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.ML.OnnxRuntime": {"target": "Package", "version": "[1.16.3, )"}, "Microsoft.ML.OnnxRuntime.Gpu": {"target": "Package", "version": "[1.16.3, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.6, )"}, "System.Diagnostics.PerformanceCounter": {"target": "Package", "version": "[8.0.0, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "fo-dicom": {"target": "Package", "version": "[5.1.2, )"}, "fo-dicom.Imaging.ImageSharp": {"target": "Package", "version": "[5.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Wpf\\MedicalImageAnalysis.Wpf.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Wpf\\MedicalImageAnalysis.Wpf.csproj", "projectName": "MedicalImageAnalysis.Wpf", "projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Wpf\\MedicalImageAnalysis.Wpf.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Wpf\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Application\\MedicalImageAnalysis.Application.csproj": {"projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Application\\MedicalImageAnalysis.Application.csproj"}, "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj": {"projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Core\\MedicalImageAnalysis.Core.csproj"}, "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\MedicalImageAnalysis.Infrastructure.csproj": {"projectPath": "D:\\AI_project\\医学影像解析\\src\\MedicalImageAnalysis.Infrastructure\\MedicalImageAnalysis.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.ML": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.ML.OnnxRuntime": {"target": "Package", "version": "[1.19.2, )"}, "Microsoft.ML.OnnxRuntime.Gpu": {"target": "Package", "version": "[1.19.2, )"}, "Microsoft.VisualBasic": {"target": "Package", "version": "[10.3.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.10.0.20240616, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.10.0.20240616, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.6, )"}, "SixLabors.ImageSharp.Drawing": {"target": "Package", "version": "[2.1.4, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "fo-dicom": {"target": "Package", "version": "[5.1.2, )"}, "fo-dicom.Imaging.ImageSharp": {"target": "Package", "version": "[5.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}