2025-07-24 04:02:34.996 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 04:02:35.031 +08:00 [INF] Hosting environment: Production
2025-07-24 04:02:35.032 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 04:02:35.617 +08:00 [ERR] 测试API连接失败
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at MedicalImageAnalysis.WpfClient.Services.ApiService.TestConnectionAsync() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.WpfClient\Services\ApiService.cs:line 53
2025-07-24 04:02:35.638 +08:00 [WRN] 无法连接到API服务
2025-07-24 04:04:00.193 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 04:04:00.225 +08:00 [INF] Hosting environment: Production
2025-07-24 04:04:00.226 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 04:04:00.670 +08:00 [INF] HttpClient BaseAddress: null
2025-07-24 04:04:00.672 +08:00 [ERR] 测试API连接失败
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at MedicalImageAnalysis.WpfClient.Services.ApiService.TestConnectionAsync() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.WpfClient\Services\ApiService.cs:line 54
2025-07-24 04:04:00.682 +08:00 [WRN] 无法连接到API服务
2025-07-24 06:27:11.211 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 06:27:11.239 +08:00 [INF] Hosting environment: Production
2025-07-24 06:27:11.240 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 06:27:11.247 +08:00 [INF] 设置HttpClient BaseAddress: http://localhost:5000
2025-07-24 06:27:11.662 +08:00 [INF] HttpClient BaseAddress: "http://localhost:5000/"
2025-07-24 06:27:11.669 +08:00 [INF] Start processing HTTP request GET http://localhost:5000/health
2025-07-24 06:27:11.672 +08:00 [INF] Sending HTTP request GET http://localhost:5000/health
2025-07-24 15:47:20.984 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 15:47:21.011 +08:00 [INF] Hosting environment: Production
2025-07-24 15:47:21.012 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 15:47:21.019 +08:00 [INF] 设置HttpClient BaseAddress: http://localhost:5000
2025-07-24 15:47:21.457 +08:00 [INF] HttpClient BaseAddress: "http://localhost:5000/"
2025-07-24 15:47:21.464 +08:00 [INF] Start processing HTTP request GET http://localhost:5000/health
2025-07-24 15:47:21.467 +08:00 [INF] Sending HTTP request GET http://localhost:5000/health
2025-07-24 22:19:29.931 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 22:19:29.958 +08:00 [INF] Hosting environment: Production
2025-07-24 22:19:29.959 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 22:19:29.968 +08:00 [INF] 设置HttpClient BaseAddress: http://localhost:5000
2025-07-24 22:19:30.404 +08:00 [INF] HttpClient BaseAddress: "http://localhost:5000/"
2025-07-24 22:19:30.410 +08:00 [INF] Start processing HTTP request GET http://localhost:5000/health
2025-07-24 22:19:30.413 +08:00 [INF] Sending HTTP request GET http://localhost:5000/health
2025-07-24 22:19:50.904 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 22:19:50.935 +08:00 [INF] Hosting environment: Production
2025-07-24 22:19:50.936 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 22:19:50.942 +08:00 [INF] 设置HttpClient BaseAddress: http://localhost:5000
2025-07-24 22:19:51.374 +08:00 [INF] HttpClient BaseAddress: "http://localhost:5000/"
2025-07-24 22:19:51.381 +08:00 [INF] Start processing HTTP request GET http://localhost:5000/health
2025-07-24 22:19:51.383 +08:00 [INF] Sending HTTP request GET http://localhost:5000/health
2025-07-24 22:20:55.762 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 22:20:55.797 +08:00 [INF] Hosting environment: Production
2025-07-24 22:20:55.798 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 22:20:55.806 +08:00 [INF] 设置HttpClient BaseAddress: http://localhost:5000
2025-07-24 22:20:56.514 +08:00 [INF] HttpClient BaseAddress: "http://localhost:5000/"
2025-07-24 22:20:56.529 +08:00 [INF] Start processing HTTP request GET http://localhost:5000/health
2025-07-24 22:20:56.534 +08:00 [INF] Sending HTTP request GET http://localhost:5000/health
2025-07-24 22:59:58.332 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 22:59:58.369 +08:00 [INF] Hosting environment: Production
2025-07-24 22:59:58.370 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 22:59:58.379 +08:00 [INF] 设置HttpClient BaseAddress: http://localhost:5000
2025-07-24 22:59:58.905 +08:00 [INF] HttpClient BaseAddress: "http://localhost:5000/"
2025-07-24 22:59:58.912 +08:00 [INF] Start processing HTTP request GET http://localhost:5000/health
2025-07-24 22:59:58.915 +08:00 [INF] Sending HTTP request GET http://localhost:5000/health
2025-07-24 23:00:17.591 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 23:00:17.632 +08:00 [INF] Hosting environment: Production
2025-07-24 23:00:17.634 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 23:00:17.642 +08:00 [INF] 设置HttpClient BaseAddress: http://localhost:5000
2025-07-24 23:00:18.208 +08:00 [INF] HttpClient BaseAddress: "http://localhost:5000/"
2025-07-24 23:00:18.219 +08:00 [INF] Start processing HTTP request GET http://localhost:5000/health
2025-07-24 23:00:18.222 +08:00 [INF] Sending HTTP request GET http://localhost:5000/health
2025-07-24 23:00:48.988 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-24 23:00:49.018 +08:00 [INF] Hosting environment: Production
2025-07-24 23:00:49.019 +08:00 [INF] Content root path: D:\AI_project\医学影像解析
2025-07-24 23:00:49.027 +08:00 [INF] 设置HttpClient BaseAddress: http://localhost:5000
2025-07-24 23:00:49.485 +08:00 [INF] HttpClient BaseAddress: "http://localhost:5000/"
2025-07-24 23:00:49.493 +08:00 [INF] Start processing HTTP request GET http://localhost:5000/health
2025-07-24 23:00:49.496 +08:00 [INF] Sending HTTP request GET http://localhost:5000/health
