﻿#pragma checksum "..\..\..\..\Views\ImageProcessingView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2CCC7761793968D010A03CB834A6D421DEECBAF3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalImageAnalysis.Wpf.Views {
    
    
    /// <summary>
    /// ImageProcessingView
    /// </summary>
    public partial class ImageProcessingView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 98 "..\..\..\..\Views\ImageProcessingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ImageScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\ImageProcessingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PlaceholderPanel;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\ImageProcessingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image MainImage;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\ImageProcessingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ImageInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Views\ImageProcessingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageInfoText;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Views\ImageProcessingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider BrightnessSlider;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\ImageProcessingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ContrastSlider;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Views\ImageProcessingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider GammaSlider;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalImageAnalysis.Wpf;V1.0.0.0;component/views/imageprocessingview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ImageProcessingView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 55 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenImage_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 69 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveImage_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 83 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetImage_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ImageScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 5:
            this.PlaceholderPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 6:
            this.MainImage = ((System.Windows.Controls.Image)(target));
            return;
            case 7:
            this.ImageInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.ImageInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.BrightnessSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 173 "..\..\..\..\Views\ImageProcessingView.xaml"
            this.BrightnessSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.BrightnessSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ContrastSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 189 "..\..\..\..\Views\ImageProcessingView.xaml"
            this.ContrastSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.ContrastSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.GammaSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 205 "..\..\..\..\Views\ImageProcessingView.xaml"
            this.GammaSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.GammaSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 220 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyGaussianBlur_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 224 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplySharpen_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 228 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyEdgeDetection_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 232 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyDenoising_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 242 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyHistogramEqualization_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 246 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyAdaptiveHistogramEqualization_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 256 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyErosion_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 260 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyDilation_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 264 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyOpening_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 268 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyClosing_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 277 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyAllChanges_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 281 "..\..\..\..\Views\ImageProcessingView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetAllParameters_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

