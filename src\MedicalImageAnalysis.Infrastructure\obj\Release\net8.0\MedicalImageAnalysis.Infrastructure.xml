<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MedicalImageAnalysis.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:MedicalImageAnalysis.Infrastructure.Data.DbInitializer">
            <summary>
            数据库初始化器
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Data.DbInitializer.InitializeAsync(System.IServiceProvider)">
            <summary>
            初始化数据库
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Data.DbInitializer.SeedDataAsync(MedicalImageAnalysis.Infrastructure.Data.MedicalImageDbContext,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            种子数据
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Data.DbInitializer.SeedSystemConfigurationsAsync(MedicalImageAnalysis.Infrastructure.Data.MedicalImageDbContext,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            种子系统配置
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Data.DbInitializer.SeedAnnotationTemplatesAsync(MedicalImageAnalysis.Infrastructure.Data.MedicalImageDbContext,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            种子标注模板
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Data.DbInitializer.SeedTrainingDatasetsAsync(MedicalImageAnalysis.Infrastructure.Data.MedicalImageDbContext,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            种子训练数据集
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Data.DatabaseServiceExtensions">
            <summary>
            数据库服务扩展
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Data.DatabaseServiceExtensions.AddDatabaseServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
            <summary>
            添加数据库服务
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Data.DatabaseServiceExtensions.InitializeDatabaseAsync(System.IServiceProvider)">
            <summary>
            初始化数据库
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Data.MedicalImageDbContext">
            <summary>
            医学影像分析系统数据库上下文
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Data.Repository`1">
            <summary>
            通用仓储实现
            </summary>
            <typeparam name="T">实体类型</typeparam>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Data.UnitOfWork">
            <summary>
            工作单元实现
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.AdvancedImageProcessingService">
            <summary>
            高级图像处理服务，提供专业的医学影像处理算法
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AdvancedImageProcessingService.MultiPlanarReconstructionAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Interfaces.PixelData},MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane,System.Int32,System.Threading.CancellationToken)">
            <summary>
            多平面重建 (MPR)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AdvancedImageProcessingService.AdvancedEdgeDetectionAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.EdgeDetectionMethod,System.Double,System.Threading.CancellationToken)">
            <summary>
            高级边缘检测
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AdvancedImageProcessingService.MorphologicalOperationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.MorphologicalOperation,MedicalImageAnalysis.Core.Interfaces.StructuringElement,System.Int32,System.Threading.CancellationToken)">
            <summary>
            形态学操作
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AdvancedImageProcessingService.FrequencyDomainFilterAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.FrequencyFilter,System.Double,System.Threading.CancellationToken)">
            <summary>
            频域滤波
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AdvancedImageProcessingService.TextureAnalysisAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.TextureFeatures,System.Threading.CancellationToken)">
            <summary>
            纹理分析
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AdvancedImageProcessingService.ImageRegistrationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.RegistrationMethod,MedicalImageAnalysis.Core.Interfaces.RegistrationParameters,System.Threading.CancellationToken)">
            <summary>
            图像配准
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.AnnotationService">
            <summary>
            智能标注服务实现，提供自动标注和标注管理功能
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.GenerateAnnotationsAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig,System.Threading.CancellationToken)">
            <summary>
            自动生成标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.BatchGenerateAnnotationsAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.DicomInstance},MedicalImageAnalysis.Core.Interfaces.AutoAnnotationConfig,System.IProgress{MedicalImageAnalysis.Core.Interfaces.AnnotationProgress},System.Threading.CancellationToken)">
            <summary>
            批量自动标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.ValidateAnnotationsAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Interfaces.AnnotationValidationRules)">
            <summary>
            验证标注质量
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.OptimizeBoundingBoxAsync(MedicalImageAnalysis.Core.Entities.Annotation,MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.BoundingBoxOptimizationConfig)">
            <summary>
            优化边界框
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.ConvertAnnotationFormatAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Interfaces.AnnotationFormat,System.ValueTuple{System.Int32,System.Int32},System.Collections.Generic.Dictionary{System.String,System.Int32})">
            <summary>
            转换标注格式
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.ExportTrainingDatasetAsync(MedicalImageAnalysis.Core.Entities.DicomStudy,MedicalImageAnalysis.Core.Interfaces.DatasetExportConfig,System.String,System.IProgress{MedicalImageAnalysis.Core.Entities.ExportProgress},System.Threading.CancellationToken)">
            <summary>
            导出训练数据集
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.GenerateAnnotationStatisticsAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation})">
            <summary>
            生成标注统计信息
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.DetectAnnotationAnomaliesAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.AnomalyDetectionConfig)">
            <summary>
            检测标注异常
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.RecommendAnnotationsAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.AnnotationRecommendationConfig)">
            <summary>
            智能推荐标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.MergeOverlappingAnnotationsAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.AnnotationMergeConfig)">
            <summary>
            合并重叠标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.FindDuplicateAnnotations(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},System.Double)">
            <summary>
            查找重复标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.AnnotationService.CalculateIoU(MedicalImageAnalysis.Core.Entities.BoundingBox,MedicalImageAnalysis.Core.Entities.BoundingBox)">
            <summary>
            计算 IoU (Intersection over Union)
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.ConnectionManagerService">
            <summary>
            连接管理服务
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ConnectionManagerService.AddConnection(System.String,System.String,System.String,System.String)">
            <summary>
            添加连接
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ConnectionManagerService.RemoveConnection(System.String)">
            <summary>
            移除连接
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ConnectionManagerService.UpdateConnectionActivity(System.String)">
            <summary>
            更新连接活动时间
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ConnectionManagerService.AddConnectionToGroup(System.String,System.String)">
            <summary>
            添加连接到组
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ConnectionManagerService.RemoveConnectionFromGroup(System.String,System.String)">
            <summary>
            从组中移除连接
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ConnectionManagerService.GetConnectionStatistics">
            <summary>
            获取连接统计信息
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ConnectionManagerService.CleanupExpiredConnectionsAsync">
            <summary>
            清理过期连接
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ConnectionManagerService.SendConnectionStatisticsAsync">
            <summary>
            发送连接统计更新
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.ConnectionStatistics">
            <summary>
            连接统计信息
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.EnhancedProcessingHub">
            <summary>
            增强的ProcessingHub，集成连接管理
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.DataAugmentationService">
            <summary>
            数据增强服务，提供多种数据增强策略
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DataAugmentationService.AugmentDataAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Interfaces.TrainingData},MedicalImageAnalysis.Core.Interfaces.DataAugmentationConfig,System.IProgress{MedicalImageAnalysis.Core.Interfaces.AugmentationProgress},System.Threading.CancellationToken)">
            <summary>
            执行数据增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DataAugmentationService.GeometricAugmentationAsync(MedicalImageAnalysis.Core.Interfaces.TrainingData,MedicalImageAnalysis.Core.Interfaces.GeometricAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            几何变换增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DataAugmentationService.ImageQualityAugmentationAsync(MedicalImageAnalysis.Core.Interfaces.TrainingData,MedicalImageAnalysis.Core.Interfaces.ImageQualityAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            图像质量增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DataAugmentationService.NoiseAugmentationAsync(MedicalImageAnalysis.Core.Interfaces.TrainingData,MedicalImageAnalysis.Core.Interfaces.NoiseAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            噪声增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DataAugmentationService.MedicalSpecificAugmentationAsync(MedicalImageAnalysis.Core.Interfaces.TrainingData,MedicalImageAnalysis.Core.Interfaces.MedicalAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            医学影像特定增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DataAugmentationService.MixupAugmentationAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Interfaces.TrainingData},MedicalImageAnalysis.Core.Interfaces.MixupConfig,System.Threading.CancellationToken)">
            <summary>
            混合增强策略
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.DicomService">
            <summary>
            DICOM 服务实现，提供高精度的 DICOM 文件处理功能
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.ParseDicomFilesAsync(System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
            <summary>
            解析 DICOM 文件并创建研究
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.ParseDicomFileAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            解析单个 DICOM 文件
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.ValidateDicomFileAsync(System.String)">
            <summary>
            验证 DICOM 文件的有效性
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.ExtractMetadataAsync(System.String)">
            <summary>
            提取 DICOM 文件的元数据
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.GetPixelDataAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            获取 DICOM 实例的像素数据
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.ConvertToHounsfieldUnitsAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Entities.DicomInstance)">
            <summary>
            将像素数据转换为 Hounsfield 单位
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.ApplyWindowLevelAsync(System.Double[],System.Double,System.Double)">
            <summary>
            应用窗宽窗位调整
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.DetectImageOrientationAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.DicomInstance})">
            <summary>
            检测影像方向
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.SortInstancesAsync(System.Collections.Generic.IEnumerable{MedicalImageAnalysis.Core.Entities.DicomInstance})">
            <summary>
            排序 DICOM 实例
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.CalculateStudyStatisticsAsync(MedicalImageAnalysis.Core.Entities.DicomStudy)">
            <summary>
            计算研究的统计信息
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.CreateOrGetSeriesAsync(FellowOakDicom.DicomDataset,MedicalImageAnalysis.Core.Entities.DicomInstance)">
            <summary>
            创建或获取序列
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.DeterminePixelDataType(MedicalImageAnalysis.Core.Entities.DicomInstance)">
            <summary>
            确定像素数据类型
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.DetermineArrayType(MedicalImageAnalysis.Core.Entities.DicomInstance)">
            <summary>
            确定数组类型
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.ParseDicomDateTime(System.String,System.String)">
            <summary>
            解析 DICOM 日期时间
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.ParseDicomDate(System.String)">
            <summary>
            解析 DICOM 日期
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.ParseGender(System.String)">
            <summary>
            解析性别
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DicomService.CalculateFileHashAsync(System.String)">
            <summary>
            计算文件哈希值
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.DirectoryService">
            <summary>
            目录管理服务实现
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.GetSystemDirectoriesAsync">
            <summary>
            获取系统目录信息
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.OpenDirectoryAsync(System.String)">
            <summary>
            打开指定目录
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.GetDirectoryContentAsync(System.String,System.Boolean)">
            <summary>
            获取目录内容
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.CreateDirectoryAsync(System.String)">
            <summary>
            创建目录
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.DeleteDirectoryAsync(System.String,System.Boolean)">
            <summary>
            删除目录
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.GetDirectorySizeAsync(System.String)">
            <summary>
            获取目录大小
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.CleanupTempFilesAsync(System.Int32)">
            <summary>
            清理临时文件
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.GetDiskUsageAsync">
            <summary>
            获取磁盘使用情况
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.InitializeSystemDirectories">
            <summary>
            初始化系统目录
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.EnsureDirectoriesExistAsync">
            <summary>
            确保目录存在
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.DirectoryService.GetFileType(System.String)">
            <summary>
            获取文件类型
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService">
            <summary>
            医学影像处理服务实现，提供高性能的影像处理功能
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.PreprocessImageAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.ImagePreprocessingOptions,System.Threading.CancellationToken)">
            <summary>
            图像预处理
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.EnhanceImageAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.ImageEnhancementType,System.Collections.Generic.Dictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            图像增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.DenoiseImageAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.DenoiseType,System.Double,System.Threading.CancellationToken)">
            <summary>
            图像降噪
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.SegmentImageAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.SegmentationType,System.Collections.Generic.Dictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            图像分割
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.RegisterImagesAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.RegistrationType,System.Threading.CancellationToken)">
            <summary>
            图像配准
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.MultiplanarReconstructionAsync(MedicalImageAnalysis.Core.Interfaces.VolumeData,MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane,System.Double,System.Threading.CancellationToken)">
            <summary>
            多平面重建 (MPR)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ConvertImageFormatAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.ImageFormat,System.Int32,System.Threading.CancellationToken)">
            <summary>
            图像格式转换
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.GenerateThumbnailAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Int32,System.Int32,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            生成图像缩略图
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.CalculateImageStatisticsAsync(MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            计算图像统计信息
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.AutoAdjustWindowLevelAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            自动调整窗宽窗位
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.GetParameter``1(System.Collections.Generic.Dictionary{System.String,System.Object},System.String,``0)">
            <summary>
            获取参数值
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.GetPixelValue(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Int32,System.Int32)">
            <summary>
            获取像素值
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.SetPixelValue(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Int32,System.Int32,System.Double)">
            <summary>
            设置像素值
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ConvertPixelDataToImage(MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            将像素数据转换为 ImageSharp 图像
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.NormalizePixelDataAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.ValueTuple{System.Double,System.Double})">
            <summary>
            归一化像素数据
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ResizePixelDataAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.ValueTuple{System.Int32,System.Int32},MedicalImageAnalysis.Core.Interfaces.InterpolationMethod)">
            <summary>
            调整图像大小
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ApplyHistogramEqualizationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            应用直方图均衡化
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ApplyClaheAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.ClaheParameters)">
            <summary>
            应用 CLAHE
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.AdjustContrastAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            调整对比度
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.AdjustBrightnessAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            调整亮度
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ApplyGammaCorrectionAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            应用伽马校正
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.SharpenImageAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            锐化图像
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.EnhanceEdgesAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            增强边缘
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ApplyGaussianFilterAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            应用高斯滤波
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ApplyMedianFilterAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Int32)">
            <summary>
            应用中值滤波
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ApplyBilateralFilterAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            应用双边滤波 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ApplyNonLocalMeansAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            应用非局部均值降噪 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ApplyWienerFilterAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            应用维纳滤波 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ApplyAnisotropicDiffusionAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            应用各向异性扩散 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ThresholdSegmentationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Double)">
            <summary>
            阈值分割
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.RegionGrowingSegmentationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            区域生长分割 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.WatershedSegmentationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            分水岭分割 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ActiveContourSegmentationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            活动轮廓分割 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.LevelSetSegmentationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            水平集分割 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.GraphCutSegmentationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            图割分割 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.DeepLearningSegmentationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            深度学习分割 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.RigidRegistrationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            刚性配准 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.AffineRegistrationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            仿射配准 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.DeformableRegistrationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            可变形配准 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.NonRigidRegistrationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            非刚性配准 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ElasticRegistrationAsync(MedicalImageAnalysis.Core.Interfaces.PixelData,MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            弹性配准 (简化实现)
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ReconstructAxialPlaneAsync(MedicalImageAnalysis.Core.Interfaces.VolumeData,System.Double)">
            <summary>
            轴位重建
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ReconstructSagittalPlaneAsync(MedicalImageAnalysis.Core.Interfaces.VolumeData,System.Double)">
            <summary>
            矢状位重建
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ReconstructCoronalPlaneAsync(MedicalImageAnalysis.Core.Interfaces.VolumeData,System.Double)">
            <summary>
            冠状位重建
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ReconstructObliquePlaneAsync(MedicalImageAnalysis.Core.Interfaces.VolumeData,System.Double)">
            <summary>
            斜位重建
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ExtractSliceFromVolume(MedicalImageAnalysis.Core.Interfaces.VolumeData,System.Int32,MedicalImageAnalysis.Core.Interfaces.ReconstructionPlane)">
            <summary>
            从体数据中提取切片
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ConvertImageToPixelData(SixLabors.ImageSharp.Image{SixLabors.ImageSharp.PixelFormats.L8})">
            <summary>
            将 ImageSharp 图像转换为像素数据
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.ClonePixelData(MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            克隆像素数据
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.CountNonZeroPixels(MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            计算非零像素数量
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ImageProcessingService.CalculateBoundingBox(MedicalImageAnalysis.Core.Interfaces.PixelData)">
            <summary>
            计算边界框
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService">
            <summary>
            实时通知服务
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendTrainingProgressAsync(System.String,MedicalImageAnalysis.Core.Interfaces.TrainingProgressInfo)">
            <summary>
            发送训练进度更新
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendTrainingCompletedAsync(System.String,MedicalImageAnalysis.Core.Interfaces.TrainingResult)">
            <summary>
            发送训练完成通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendTrainingFailedAsync(System.String,System.String)">
            <summary>
            发送训练失败通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendProcessingProgressAsync(System.String,MedicalImageAnalysis.Core.Interfaces.ProcessingProgressInfo)">
            <summary>
            发送处理进度更新
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendProcessingCompletedAsync(System.String,MedicalImageAnalysis.Core.Entities.ProcessingResult)">
            <summary>
            发送处理完成通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendSystemStatusAsync(MedicalImageAnalysis.Core.Interfaces.SystemStatusInfo)">
            <summary>
            发送系统状态更新
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendNotificationAsync(MedicalImageAnalysis.Core.Interfaces.NotificationInfo)">
            <summary>
            发送通用通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendNotificationToUserAsync(System.String,MedicalImageAnalysis.Core.Interfaces.NotificationInfo)">
            <summary>
            发送给特定用户的通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendNotificationToGroupAsync(System.String,MedicalImageAnalysis.Core.Interfaces.NotificationInfo)">
            <summary>
            发送给特定组的通知
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendDicomUploadProgressAsync(System.String,MedicalImageAnalysis.Core.Interfaces.DicomUploadProgressInfo)">
            <summary>
            发送DICOM上传进度
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendAnnotationProgressAsync(System.String,MedicalImageAnalysis.Core.Interfaces.AnnotationProgressInfo)">
            <summary>
            发送标注进度更新
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.RealTimeNotificationService.SendInferenceResultAsync(System.String,MedicalImageAnalysis.Core.Interfaces.InferenceResult)">
            <summary>
            发送模型推理结果
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub">
            <summary>
            ProcessingHub - SignalR Hub实现
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.OnConnectedAsync">
            <summary>
            客户端连接时调用
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            客户端断开连接时调用
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.JoinTrainingGroup(System.String)">
            <summary>
            加入训练组
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.LeaveTrainingGroup(System.String)">
            <summary>
            离开训练组
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.JoinProcessingGroup(System.String)">
            <summary>
            加入处理组
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.LeaveProcessingGroup(System.String)">
            <summary>
            离开处理组
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.JoinUploadGroup(System.String)">
            <summary>
            加入上传组
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.JoinAnnotationGroup(System.String)">
            <summary>
            加入标注组
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.JoinInferenceGroup(System.String)">
            <summary>
            加入推理组
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.ProcessingHub.SendHeartbeat">
            <summary>
            发送心跳
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService">
            <summary>
            智能标注服务，提供高级的自动标注和标注优化功能
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.GenerateSmartAnnotationsAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.SmartAnnotationConfig,System.Threading.CancellationToken)">
            <summary>
            智能标注生成
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.AdaptiveAnnotationOptimizationAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.AdaptiveOptimizationConfig,System.Threading.CancellationToken)">
            <summary>
            自适应标注优化
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.AutoEvaluateAnnotationQualityAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.QualityEvaluationConfig,System.Threading.CancellationToken)">
            <summary>
            标注质量自动评估
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.GenerateSmartRecommendationsAsync(MedicalImageAnalysis.Core.Entities.DicomInstance,System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Interfaces.SmartRecommendationConfig,System.Threading.CancellationToken)">
            <summary>
            智能标注推荐
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.AugmentAnnotationDataAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.AnnotationAugmentationConfig,System.Threading.CancellationToken)">
            <summary>
            标注数据增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.OptimizeSingleAnnotationAsync(MedicalImageAnalysis.Core.Entities.Annotation,MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.AdaptiveOptimizationConfig,System.Threading.CancellationToken)">
            <summary>
            优化单个标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.GlobalOptimizationAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Interfaces.AdaptiveOptimizationConfig)">
            <summary>
            全局优化
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.ApplyGeometricAugmentationAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.GeometricAugmentationConfig)">
            <summary>
            应用几何增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.ApplyImageAugmentationAsync(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation},MedicalImageAnalysis.Core.Entities.DicomInstance,MedicalImageAnalysis.Core.Interfaces.ImageAugmentationConfig)">
            <summary>
            应用图像增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.RefineAnnotationEdgesAsync(MedicalImageAnalysis.Core.Entities.Annotation,MedicalImageAnalysis.Core.Entities.DicomInstance)">
            <summary>
            细化标注边缘
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.OptimizeAnnotationShapeAsync(MedicalImageAnalysis.Core.Entities.Annotation,MedicalImageAnalysis.Core.Entities.DicomInstance)">
            <summary>
            优化标注形状
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.AdjustAnnotationContextuallyAsync(MedicalImageAnalysis.Core.Entities.Annotation,MedicalImageAnalysis.Core.Entities.DicomInstance)">
            <summary>
            上下文调整标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.RemoveOverlappingAnnotations(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation})">
            <summary>
            移除重叠标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.MergeAdjacentAnnotations(System.Collections.Generic.List{MedicalImageAnalysis.Core.Entities.Annotation})">
            <summary>
            合并相邻标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.RotateAnnotation(MedicalImageAnalysis.Core.Entities.Annotation,System.Double)">
            <summary>
            旋转标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.ScaleAnnotation(MedicalImageAnalysis.Core.Entities.Annotation,System.Double)">
            <summary>
            缩放标注
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.SmartAnnotationService.CalculateIoU(MedicalImageAnalysis.Core.Entities.BoundingBox,MedicalImageAnalysis.Core.Entities.BoundingBox)">
            <summary>
            计算IoU
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.AugmentedAnnotationData">
            <summary>
            增强标注数据
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.SystemMonitoringService">
            <summary>
            系统监控服务
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.YoloService">
            <summary>
            YOLO 服务实现，提供 YOLOv11 模型的完整生命周期管理
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.TrainModelAsync(MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig,System.IProgress{MedicalImageAnalysis.Core.Interfaces.TrainingProgress},System.Threading.CancellationToken)">
            <summary>
            训练 YOLO 模型
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.ValidateModelAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            验证模型性能
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.InferAsync(System.String,System.Byte[],MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig,System.Threading.CancellationToken)">
            <summary>
            使用模型进行推理
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.BatchInferAsync(System.String,System.Collections.Generic.IEnumerable{System.String},MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig,System.IProgress{System.Int32},System.Threading.CancellationToken)">
            <summary>
            批量推理
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.ExportModelAsync(System.String,MedicalImageAnalysis.Core.Interfaces.ModelExportFormat,System.String,System.Threading.CancellationToken)">
            <summary>
            导出模型为不同格式
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GetModelInfoAsync(System.String)">
            <summary>
            获取模型信息
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.CreateDatasetConfigAsync(MedicalImageAnalysis.Core.Entities.DatasetConfig,System.String)">
            <summary>
            创建数据集配置文件
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.ValidateDatasetAsync(System.String)">
            <summary>
            验证数据集格式
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GenerateDataAugmentationAsync(System.String,MedicalImageAnalysis.Core.Interfaces.DataAugmentationConfig,System.String,System.Threading.CancellationToken)">
            <summary>
            生成数据增强
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.FindPythonExecutable">
            <summary>
            查找 Python 可执行文件
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.ValidateTrainingConfig(MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig)">
            <summary>
            验证训练配置
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GenerateTrainingScriptAsync(MedicalImageAnalysis.Core.Interfaces.YoloTrainingConfig,System.String)">
            <summary>
            生成训练脚本
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GenerateValidationScriptAsync(System.String,System.String)">
            <summary>
            生成验证脚本
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GenerateInferenceScriptAsync(System.String,System.String,MedicalImageAnalysis.Core.Interfaces.YoloInferenceConfig)">
            <summary>
            生成推理脚本
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.ExecutePythonScriptAsync(System.String,System.IProgress{MedicalImageAnalysis.Core.Interfaces.TrainingProgress},System.Threading.CancellationToken)">
            <summary>
            执行 Python 脚本
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.TryParseTrainingProgress(System.String,System.IProgress{MedicalImageAnalysis.Core.Interfaces.TrainingProgress})">
            <summary>
            尝试解析训练进度
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GenerateExportScriptAsync(System.String,MedicalImageAnalysis.Core.Interfaces.ModelExportFormat,System.String)">
            <summary>
            生成导出脚本
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GenerateModelInfoScriptAsync(System.String)">
            <summary>
            生成模型信息获取脚本
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GenerateAugmentationScriptAsync(System.String,MedicalImageAnalysis.Core.Interfaces.DataAugmentationConfig,System.String)">
            <summary>
            生成数据增强脚本
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GetExportedModelPath(System.String,MedicalImageAnalysis.Core.Interfaces.ModelExportFormat)">
            <summary>
            获取导出模型路径
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.GenerateYamlConfig(MedicalImageAnalysis.Core.Entities.DatasetConfig)">
            <summary>
            生成 YAML 配置
            </summary>
        </member>
        <member name="M:MedicalImageAnalysis.Infrastructure.Services.YoloService.CreatePythonScripts">
            <summary>
            创建Python脚本文件
            </summary>
        </member>
        <member name="T:MedicalImageAnalysis.Infrastructure.Services.ProcessResult">
            <summary>
            进程执行结果
            </summary>
        </member>
    </members>
</doc>
