2025-07-25 08:41:17.403 +08:00 [INF] 医学影像解析系统启动中...
2025-07-25 08:41:17.436 +08:00 [INF] 应用程序版本: 1.0.0.0
2025-07-25 08:41:17.439 +08:00 [INF] 运行环境: Microsoft Windows NT 10.0.19045.0
2025-07-25 08:41:17.439 +08:00 [INF] 工作目录: D:\AI_project\医学影像解析
2025-07-25 08:41:17.439 +08:00 [INF] 使用默认主题，跳过MaterialDesign主题初始化
2025-07-25 08:41:17.497 +08:00 [INF] 依赖注入配置完成
2025-07-25 08:41:17.730 +08:00 [INF] 医学影像解析系统主窗口已初始化
2025-07-25 08:41:17.917 +08:00 [INF] 主窗口创建完成
2025-07-25 08:41:17.918 +08:00 [INF] 医学影像解析系统启动成功
2025-07-25 08:41:23.927 +08:00 [INF] 正在初始化增强DICOM服务...
2025-07-25 08:41:23.927 +08:00 [INF] 增强DICOM服务初始化完成
2025-07-25 08:41:23.928 +08:00 [INF] GDCM DICOM查看器初始化完成
2025-07-25 08:41:23.929 +08:00 [INF] GDCM DICOM查看器页面已加载
2025-07-25 08:41:29.829 +08:00 [INF] 开始解析DICOM文件: D:\AI_project\医学影像解析\Brain\DJ01.dcm
2025-07-25 08:41:29.837 +08:00 [INF] DICOM文件解析完成: 1.3.46.670589.33.1.63888257485567488200001.5659266304615743079
2025-07-25 08:41:29.852 +08:00 [INF] 开始提取DICOM图像: D:\AI_project\医学影像解析\Brain\DJ01.dcm
2025-07-25 08:41:29.853 +08:00 [INF] 应用窗宽窗位调整: WC=0, WW=0
2025-07-25 08:41:29.852 +08:00 [INF] 应用窗宽窗位调整: WC=0, WW=400
2025-07-25 08:41:29.885 +08:00 [ERR] 提取DICOM图像失败: D:\AI_project\医学影像解析\Brain\DJ01.dcm
System.NullReferenceException: Object reference not set to an instance of an object.
   at FellowOakDicom.Imaging.ImageSharpImageExtensions.AsSharpImage(IImage iimage)
   at MedicalImageAnalysis.Wpf.Services.GdcmDicomService.<>c__DisplayClass8_0.<ExtractImageAsync>b__0() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Services\GdcmDicomService.cs:line 263
2025-07-25 08:41:29.885 +08:00 [ERR] 应用窗宽窗位调整失败
System.NullReferenceException: Object reference not set to an instance of an object.
   at FellowOakDicom.Imaging.ImageSharpImageExtensions.AsSharpImage(IImage iimage)
   at MedicalImageAnalysis.Wpf.Services.GdcmImageProcessor.<>c__DisplayClass2_0.<ApplyWindowLevelAsync>b__0() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Services\GdcmImageProcessor.cs:line 45
2025-07-25 08:41:29.885 +08:00 [ERR] 应用窗宽窗位调整失败
System.NullReferenceException: Object reference not set to an instance of an object.
   at FellowOakDicom.Imaging.ImageSharpImageExtensions.AsSharpImage(IImage iimage)
   at MedicalImageAnalysis.Wpf.Services.GdcmImageProcessor.<>c__DisplayClass2_0.<ApplyWindowLevelAsync>b__0() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Services\GdcmImageProcessor.cs:line 45
2025-07-25 08:41:29.930 +08:00 [INF] DICOM文件加载完成: D:\AI_project\医学影像解析\Brain\DJ01.dcm
2025-07-25 08:41:49.929 +08:00 [INF] 应用窗宽窗位调整: WC=0, WW=0
2025-07-25 08:41:49.937 +08:00 [ERR] 应用窗宽窗位调整失败
System.NullReferenceException: Object reference not set to an instance of an object.
   at FellowOakDicom.Imaging.ImageSharpImageExtensions.AsSharpImage(IImage iimage)
   at MedicalImageAnalysis.Wpf.Services.GdcmImageProcessor.<>c__DisplayClass2_0.<ApplyWindowLevelAsync>b__0() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Services\GdcmImageProcessor.cs:line 45
2025-07-25 08:41:55.626 +08:00 [INF] 开始解析DICOM文件: D:\AI_project\医学影像解析\Brain\DJ01.dcm
2025-07-25 08:41:55.627 +08:00 [INF] DICOM文件解析完成: 1.3.46.670589.33.1.63888257485567488200001.5659266304615743079
2025-07-25 08:41:55.627 +08:00 [INF] 开始提取DICOM图像: D:\AI_project\医学影像解析\Brain\DJ01.dcm
2025-07-25 08:41:55.635 +08:00 [ERR] 提取DICOM图像失败: D:\AI_project\医学影像解析\Brain\DJ01.dcm
System.NullReferenceException: Object reference not set to an instance of an object.
   at FellowOakDicom.Imaging.ImageSharpImageExtensions.AsSharpImage(IImage iimage)
   at MedicalImageAnalysis.Wpf.Services.GdcmDicomService.<>c__DisplayClass8_0.<ExtractImageAsync>b__0() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Services\GdcmDicomService.cs:line 263
2025-07-25 08:41:55.650 +08:00 [INF] DICOM文件加载完成: D:\AI_project\医学影像解析\Brain\DJ01.dcm
2025-07-25 08:52:56.551 +08:00 [INF] 医学影像解析系统启动中...
2025-07-25 08:52:56.580 +08:00 [INF] 应用程序版本: 1.0.0.0
2025-07-25 08:52:56.582 +08:00 [INF] 运行环境: Microsoft Windows NT 10.0.19045.0
2025-07-25 08:52:56.583 +08:00 [INF] 工作目录: D:\AI_project\医学影像解析
2025-07-25 08:52:56.583 +08:00 [INF] 使用默认主题，跳过MaterialDesign主题初始化
2025-07-25 08:52:56.631 +08:00 [INF] 依赖注入配置完成
2025-07-25 08:52:56.831 +08:00 [INF] 医学影像解析系统主窗口已初始化
2025-07-25 08:52:57.023 +08:00 [INF] 主窗口创建完成
2025-07-25 08:52:57.023 +08:00 [INF] 医学影像解析系统启动成功
2025-07-25 08:52:58.725 +08:00 [INF] 正在初始化增强DICOM服务...
2025-07-25 08:52:58.725 +08:00 [INF] 增强DICOM服务初始化完成
2025-07-25 08:52:58.727 +08:00 [INF] GDCM DICOM查看器初始化完成
2025-07-25 08:52:58.728 +08:00 [INF] GDCM DICOM查看器页面已加载
2025-07-25 08:53:05.771 +08:00 [INF] 开始解析DICOM文件: D:\AI_project\医学影像解析\Brain\DJ01.dcm
2025-07-25 08:53:05.779 +08:00 [INF] DICOM文件解析完成: 1.3.46.670589.33.1.63888257485567488200001.5659266304615743079
2025-07-25 08:53:05.783 +08:00 [INF] 应用窗宽窗位调整: WC=0, WW=400
2025-07-25 08:53:05.785 +08:00 [INF] 应用窗宽窗位调整: WC=0, WW=0
2025-07-25 08:53:05.785 +08:00 [INF] 开始提取DICOM图像: D:\AI_project\医学影像解析\Brain\DJ01.dcm
2025-07-25 08:53:05.786 +08:00 [INF] 应用窗宽窗位: WC=0, WW=0
2025-07-25 08:53:05.786 +08:00 [INF] 应用窗宽窗位: WC=0, WW=400
2025-07-25 08:53:05.786 +08:00 [INF] 开始提取像素数据: 512x512, 16位
2025-07-25 08:53:05.789 +08:00 [INF] 原始像素数据长度: 524288 字节
2025-07-25 08:53:05.796 +08:00 [INF] 成功创建BitmapSource: 512x512
2025-07-25 08:53:05.796 +08:00 [INF] DICOM图像提取完成: 512x512
2025-07-25 08:53:05.835 +08:00 [INF] DICOM文件加载完成: D:\AI_project\医学影像解析\Brain\DJ01.dcm
2025-07-25 08:53:17.979 +08:00 [INF] 转换DICOM为图像格式: PNG
2025-07-25 08:53:18.001 +08:00 [ERR] DICOM转换失败
System.NullReferenceException: Object reference not set to an instance of an object.
   at FellowOakDicom.Imaging.ImageSharpImageExtensions.AsSharpImage(IImage iimage)
   at MedicalImageAnalysis.Wpf.Services.GdcmImageProcessor.<>c__DisplayClass10_0.<ConvertDicomToImageAsync>b__0() in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Services\GdcmImageProcessor.cs:line 308
2025-07-25 08:53:18.012 +08:00 [ERR] 导出图像失败
System.InvalidOperationException: 图像导出失败
   at MedicalImageAnalysis.Wpf.Views.GdcmDicomViewer.ExportImageButton_Click(Object sender, RoutedEventArgs e) in D:\AI_project\医学影像解析\src\MedicalImageAnalysis.Wpf\Views\GdcmDicomViewer.xaml.cs:line 306
